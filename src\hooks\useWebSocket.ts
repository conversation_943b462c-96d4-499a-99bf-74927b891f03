/**
 * WebSocket hook for real-time chat communication
 */
import { useEffect, useRef, useCallback } from 'react';
import { io, Socket } from 'socket.io-client';
import { useChatStore } from '../store/chatStore';
import { apiClient } from '../lib/api';

interface ChatRequest {
  session_id: number;
  message: string;
  file_attachments?: any[];
  user_preferences?: any;
}

interface WebSocketMessage {
  type: 'message' | 'status' | 'error';
  data?: any;
  message?: string;
  message_id?: number;
  status?: string;
  details?: string;
}

export const useWebSocket = () => {
  const socketRef = useRef<Socket | null>(null);
  const {
    setIsConnected,
    addMessage,
    updateMessage,
    currentSession
  } = useChatStore();

  const connect = useCallback(() => {
    if (!apiClient.isAuthenticated()) {
      console.warn('Cannot connect WebSocket: not authenticated');
      return;
    }

    // Disconnect existing connection
    if (socketRef.current) {
      socketRef.current.disconnect();
    }

    try {
      // Create WebSocket connection using Socket.IO
      const wsUrl = apiClient.getWebSocketUrl().replace('/ws/', '/');
      
      socketRef.current = io(wsUrl, {
        auth: {
          token: apiClient.getAccessToken()
        },
        transports: ['websocket'],
        upgrade: false
      });

      const socket = socketRef.current;

      socket.on('connect', () => {
        console.log('WebSocket connected');
        setIsConnected(true);
      });

      socket.on('disconnect', (reason) => {
        console.log('WebSocket disconnected:', reason);
        setIsConnected(false);
      });

      socket.on('connect_error', (error) => {
        console.error('WebSocket connection error:', error);
        setIsConnected(false);
      });

      // Handle incoming messages
      socket.on('message', (data: WebSocketMessage) => {
        handleWebSocketMessage(data);
      });

      socket.on('error', (error: any) => {
        console.error('WebSocket error:', error);
      });

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setIsConnected(false);
    }
  }, [setIsConnected]);

  const disconnect = useCallback(() => {
    if (socketRef.current) {
      socketRef.current.disconnect();
      socketRef.current = null;
      setIsConnected(false);
    }
  }, [setIsConnected]);

  const sendMessage = useCallback((chatRequest: ChatRequest) => {
    if (!socketRef.current || !socketRef.current.connected) {
      console.error('WebSocket not connected');
      return false;
    }

    try {
      socketRef.current.emit('chat_message', chatRequest);
      return true;
    } catch (error) {
      console.error('Failed to send message:', error);
      return false;
    }
  }, []);

  const handleWebSocketMessage = useCallback((data: WebSocketMessage) => {
    switch (data.type) {
      case 'message':
        if (data.data) {
          addMessage(data.data);
        }
        break;

      case 'status':
        if (data.message_id && data.status) {
          updateMessage(data.message_id, { status: data.status as any });
        }
        break;

      case 'error':
        console.error('WebSocket error message:', data.message, data.details);
        // You could show a toast notification here
        break;

      default:
        console.warn('Unknown WebSocket message type:', data.type);
    }
  }, [addMessage, updateMessage]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (apiClient.isAuthenticated()) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  // Reconnect when session changes
  useEffect(() => {
    if (currentSession && socketRef.current && !socketRef.current.connected) {
      connect();
    }
  }, [currentSession, connect]);

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: socketRef.current?.connected || false
  };
};

// Alternative WebSocket implementation using native WebSocket API
export const useNativeWebSocket = () => {
  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const {
    setIsConnected,
    addMessage,
    updateMessage
  } = useChatStore();

  const connect = useCallback(() => {
    if (!apiClient.isAuthenticated()) {
      console.warn('Cannot connect WebSocket: not authenticated');
      return;
    }

    // Clear any existing reconnect timeout
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    // Close existing connection
    if (wsRef.current) {
      wsRef.current.close();
    }

    try {
      const wsUrl = apiClient.getWebSocketUrl();
      wsRef.current = new WebSocket(wsUrl);

      wsRef.current.onopen = () => {
        console.log('WebSocket connected');
        setIsConnected(true);
      };

      wsRef.current.onclose = (event) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setIsConnected(false);

        // Auto-reconnect after 3 seconds if not a normal closure
        if (event.code !== 1000 && apiClient.isAuthenticated()) {
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, 3000);
        }
      };

      wsRef.current.onerror = (error) => {
        console.error('WebSocket error:', error);
        setIsConnected(false);
      };

      wsRef.current.onmessage = (event) => {
        try {
          const data: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(data);
        } catch (error) {
          console.error('Failed to parse WebSocket message:', error);
        }
      };

    } catch (error) {
      console.error('Failed to create WebSocket connection:', error);
      setIsConnected(false);
    }
  }, [setIsConnected]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close(1000, 'User disconnected');
      wsRef.current = null;
      setIsConnected(false);
    }
  }, [setIsConnected]);

  const sendMessage = useCallback((chatRequest: ChatRequest) => {
    if (!wsRef.current || wsRef.current.readyState !== WebSocket.OPEN) {
      console.error('WebSocket not connected');
      return false;
    }

    try {
      wsRef.current.send(JSON.stringify(chatRequest));
      return true;
    } catch (error) {
      console.error('Failed to send message:', error);
      return false;
    }
  }, []);

  const handleWebSocketMessage = useCallback((data: WebSocketMessage) => {
    switch (data.type) {
      case 'message':
        if (data.data) {
          addMessage(data.data);
        }
        break;

      case 'status':
        if (data.message_id && data.status) {
          updateMessage(data.message_id, { status: data.status as any });
        }
        break;

      case 'error':
        console.error('WebSocket error message:', data.message, data.details);
        break;

      default:
        console.warn('Unknown WebSocket message type:', data.type);
    }
  }, [addMessage, updateMessage]);

  // Auto-connect when authenticated
  useEffect(() => {
    if (apiClient.isAuthenticated()) {
      connect();
    }

    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    connect,
    disconnect,
    sendMessage,
    isConnected: wsRef.current?.readyState === WebSocket.OPEN
  };
};
