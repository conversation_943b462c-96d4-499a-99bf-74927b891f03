"""
Tool Registry for managing and executing various tools with fallback mechanisms.
"""
import asyncio
import inspect
from typing import Dict, List, Any, Optional, Callable, Union
from dataclasses import dataclass, field
from enum import Enum
import structlog
from abc import ABC, abstractmethod

logger = structlog.get_logger(__name__)


class ToolCategory(Enum):
    """Tool categories."""
    FILE_PROCESSING = "file_processing"
    WEB_SEARCH = "web_search"
    CODE_EXECUTION = "code_execution"
    DATA_ANALYSIS = "data_analysis"
    COMMUNICATION = "communication"
    UTILITY = "utility"


class ToolStatus(Enum):
    """Tool execution status."""
    AVAILABLE = "available"
    UNAVAILABLE = "unavailable"
    DEPRECATED = "deprecated"
    MAINTENANCE = "maintenance"


@dataclass
class ToolMetadata:
    """Tool metadata."""
    name: str
    description: str
    category: ToolCategory
    version: str = "1.0.0"
    author: str = "NextNow"
    status: ToolStatus = ToolStatus.AVAILABLE
    requires_auth: bool = False
    rate_limit: Optional[int] = None  # requests per minute
    timeout: int = 30  # seconds
    fallback_tools: List[str] = field(default_factory=list)
    dependencies: List[str] = field(default_factory=list)


@dataclass
class ToolResult:
    """Tool execution result."""
    success: bool
    data: Any = None
    error: Optional[str] = None
    execution_time: float = 0.0
    tool_used: str = ""
    fallback_used: bool = False
    metadata: Dict[str, Any] = field(default_factory=dict)


class BaseTool(ABC):
    """Base class for all tools."""
    
    def __init__(self, metadata: ToolMetadata):
        self.metadata = metadata
        self._usage_count = 0
        self._error_count = 0
    
    @abstractmethod
    async def execute(self, **kwargs) -> ToolResult:
        """Execute the tool with given parameters."""
        pass
    
    async def validate_input(self, **kwargs) -> bool:
        """Validate input parameters."""
        return True
    
    def get_schema(self) -> Dict[str, Any]:
        """Get tool parameter schema."""
        sig = inspect.signature(self.execute)
        schema = {
            "name": self.metadata.name,
            "description": self.metadata.description,
            "parameters": {
                "type": "object",
                "properties": {},
                "required": []
            }
        }
        
        for param_name, param in sig.parameters.items():
            if param_name == "kwargs":
                continue
            
            param_info = {
                "type": "string",  # Default type
                "description": f"Parameter {param_name}"
            }
            
            if param.default != inspect.Parameter.empty:
                param_info["default"] = param.default
            else:
                schema["parameters"]["required"].append(param_name)
            
            schema["parameters"]["properties"][param_name] = param_info
        
        return schema
    
    @property
    def success_rate(self) -> float:
        """Calculate tool success rate."""
        if self._usage_count == 0:
            return 1.0
        return (self._usage_count - self._error_count) / self._usage_count
    
    def _record_usage(self, success: bool):
        """Record tool usage statistics."""
        self._usage_count += 1
        if not success:
            self._error_count += 1


class FileProcessorTool(BaseTool):
    """File processing tool."""
    
    def __init__(self):
        metadata = ToolMetadata(
            name="file_processor",
            description="Process and extract text from various file formats (PDF, DOCX, TXT)",
            category=ToolCategory.FILE_PROCESSING,
            timeout=60,
            fallback_tools=["simple_text_extractor"]
        )
        super().__init__(metadata)
    
    async def execute(self, file_path: str, file_type: str = "auto") -> ToolResult:
        """Execute file processing."""
        try:
            from .file_processor import process_file
            
            start_time = asyncio.get_event_loop().time()
            
            # Process the file
            extracted_text = await process_file(file_path, file_type)
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            self._record_usage(True)
            
            return ToolResult(
                success=True,
                data={"extracted_text": extracted_text, "file_path": file_path},
                execution_time=execution_time,
                tool_used=self.metadata.name
            )
            
        except Exception as e:
            self._record_usage(False)
            logger.error("File processing failed", error=str(e), file_path=file_path)
            
            return ToolResult(
                success=False,
                error=str(e),
                tool_used=self.metadata.name
            )


class WebSearchTool(BaseTool):
    """Web search tool."""
    
    def __init__(self):
        metadata = ToolMetadata(
            name="web_search",
            description="Search the web for information",
            category=ToolCategory.WEB_SEARCH,
            rate_limit=60,  # 60 requests per minute
            fallback_tools=["simple_search"]
        )
        super().__init__(metadata)
    
    async def execute(self, query: str, num_results: int = 5) -> ToolResult:
        """Execute web search."""
        try:
            # Placeholder for actual web search implementation
            # This would integrate with search APIs like Google, Bing, etc.
            
            start_time = asyncio.get_event_loop().time()
            
            # Simulate search results
            results = [
                {
                    "title": f"Result {i+1} for '{query}'",
                    "url": f"https://example.com/result-{i+1}",
                    "snippet": f"This is a sample snippet for result {i+1} about {query}"
                }
                for i in range(num_results)
            ]
            
            end_time = asyncio.get_event_loop().time()
            execution_time = end_time - start_time
            
            self._record_usage(True)
            
            return ToolResult(
                success=True,
                data={"results": results, "query": query},
                execution_time=execution_time,
                tool_used=self.metadata.name
            )
            
        except Exception as e:
            self._record_usage(False)
            logger.error("Web search failed", error=str(e), query=query)
            
            return ToolResult(
                success=False,
                error=str(e),
                tool_used=self.metadata.name
            )


class ToolRegistry:
    """Registry for managing and executing tools."""
    
    def __init__(self):
        self.tools: Dict[str, BaseTool] = {}
        self.tool_usage_stats: Dict[str, Dict[str, Any]] = {}
        self._initialize_default_tools()
    
    def _initialize_default_tools(self):
        """Initialize default tools."""
        default_tools = [
            FileProcessorTool(),
            WebSearchTool(),
        ]
        
        for tool in default_tools:
            self.register_tool(tool)
    
    def register_tool(self, tool: BaseTool):
        """Register a new tool."""
        self.tools[tool.metadata.name] = tool
        self.tool_usage_stats[tool.metadata.name] = {
            "total_calls": 0,
            "successful_calls": 0,
            "failed_calls": 0,
            "avg_execution_time": 0.0
        }
        logger.info("Tool registered", tool_name=tool.metadata.name)
    
    def unregister_tool(self, tool_name: str):
        """Unregister a tool."""
        if tool_name in self.tools:
            del self.tools[tool_name]
            del self.tool_usage_stats[tool_name]
            logger.info("Tool unregistered", tool_name=tool_name)
    
    def get_tool(self, tool_name: str) -> Optional[BaseTool]:
        """Get a tool by name."""
        return self.tools.get(tool_name)
    
    def list_tools(self, category: Optional[ToolCategory] = None) -> List[Dict[str, Any]]:
        """List available tools."""
        tools = []
        
        for tool_name, tool in self.tools.items():
            if category and tool.metadata.category != category:
                continue
            
            if tool.metadata.status != ToolStatus.AVAILABLE:
                continue
            
            tools.append({
                "name": tool.metadata.name,
                "description": tool.metadata.description,
                "category": tool.metadata.category.value,
                "version": tool.metadata.version,
                "schema": tool.get_schema(),
                "success_rate": tool.success_rate,
                "status": tool.metadata.status.value
            })
        
        return tools
    
    async def execute_tool(
        self,
        tool_name: str,
        parameters: Dict[str, Any],
        use_fallback: bool = True
    ) -> ToolResult:
        """Execute a tool with fallback support."""
        tool = self.get_tool(tool_name)
        
        if not tool:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' not found",
                tool_used=tool_name
            )
        
        if tool.metadata.status != ToolStatus.AVAILABLE:
            return ToolResult(
                success=False,
                error=f"Tool '{tool_name}' is not available (status: {tool.metadata.status.value})",
                tool_used=tool_name
            )
        
        # Validate input
        try:
            if not await tool.validate_input(**parameters):
                return ToolResult(
                    success=False,
                    error="Input validation failed",
                    tool_used=tool_name
                )
        except Exception as e:
            return ToolResult(
                success=False,
                error=f"Input validation error: {str(e)}",
                tool_used=tool_name
            )
        
        # Execute tool
        try:
            result = await asyncio.wait_for(
                tool.execute(**parameters),
                timeout=tool.metadata.timeout
            )
            
            # Update statistics
            self._update_tool_stats(tool_name, result)
            
            if result.success:
                return result
            
            # Try fallback if execution failed and fallback is enabled
            if use_fallback and tool.metadata.fallback_tools:
                logger.warning(
                    "Tool execution failed, trying fallback",
                    tool_name=tool_name,
                    error=result.error
                )
                
                for fallback_tool_name in tool.metadata.fallback_tools:
                    fallback_result = await self.execute_tool(
                        fallback_tool_name,
                        parameters,
                        use_fallback=False  # Prevent infinite recursion
                    )
                    
                    if fallback_result.success:
                        fallback_result.fallback_used = True
                        fallback_result.tool_used = f"{tool_name} -> {fallback_result.tool_used}"
                        return fallback_result
            
            return result
            
        except asyncio.TimeoutError:
            self._update_tool_stats(tool_name, ToolResult(success=False, error="Timeout"))
            return ToolResult(
                success=False,
                error=f"Tool execution timed out after {tool.metadata.timeout} seconds",
                tool_used=tool_name
            )
        
        except Exception as e:
            error_result = ToolResult(success=False, error=str(e), tool_used=tool_name)
            self._update_tool_stats(tool_name, error_result)
            logger.error("Tool execution failed", tool_name=tool_name, error=str(e))
            return error_result
    
    def _update_tool_stats(self, tool_name: str, result: ToolResult):
        """Update tool usage statistics."""
        if tool_name not in self.tool_usage_stats:
            return
        
        stats = self.tool_usage_stats[tool_name]
        stats["total_calls"] += 1
        
        if result.success:
            stats["successful_calls"] += 1
            
            # Update average execution time
            current_avg = stats["avg_execution_time"]
            total_successful = stats["successful_calls"]
            stats["avg_execution_time"] = (
                (current_avg * (total_successful - 1) + result.execution_time) / total_successful
            )
        else:
            stats["failed_calls"] += 1
    
    def get_tool_stats(self, tool_name: Optional[str] = None) -> Dict[str, Any]:
        """Get tool usage statistics."""
        if tool_name:
            return self.tool_usage_stats.get(tool_name, {})
        return self.tool_usage_stats
    
    def health_check(self) -> Dict[str, Any]:
        """Perform health check on all tools."""
        health_status = {
            "total_tools": len(self.tools),
            "available_tools": 0,
            "unavailable_tools": 0,
            "tool_details": {}
        }
        
        for tool_name, tool in self.tools.items():
            is_available = tool.metadata.status == ToolStatus.AVAILABLE
            
            if is_available:
                health_status["available_tools"] += 1
            else:
                health_status["unavailable_tools"] += 1
            
            health_status["tool_details"][tool_name] = {
                "status": tool.metadata.status.value,
                "success_rate": tool.success_rate,
                "usage_count": tool._usage_count,
                "error_count": tool._error_count
            }
        
        return health_status


# Global tool registry instance
tool_registry = ToolRegistry()
