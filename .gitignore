# Dependencies
node_modules/
backend/venv/
backend/.venv/

# Build outputs
dist/
build/
*.tsbuildinfo

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local
backend/.env
backend/.env.local
backend/.env.development.local
backend/.env.test.local
backend/.env.production.local

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
backend/logs/

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov
backend/htmlcov/
backend/.coverage
backend/coverage.xml

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# TypeScript cache
*.tsbuildinfo

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt
dist

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Editor directories and files
.vscode/*
!.vscode/extensions.json
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/
.pytest_cache/
.mypy_cache/
.dmypy.json
dmypy.json

# Database
*.db
*.sqlite
*.sqlite3

# Docker
.dockerignore

# Uploads and user data
backend/uploads/*
!backend/uploads/.gitkeep

# SSL certificates
nginx/ssl/*
!nginx/ssl/.gitkeep

# Monitoring data
monitoring/data/

# Backup files
backups/
*.backup
*.bak

# Local development
.local
*.local

# Test files
test-results/
playwright-report/
test-results.xml

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl

# Kubernetes
*.kubeconfig

# Secrets
secrets/
*.pem
*.key
*.crt
*.p12
*.pfx

# IDE
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
.project
.classpath
.settings/

# Misc
*.tgz
*.tar.gz
.cache/
.env
