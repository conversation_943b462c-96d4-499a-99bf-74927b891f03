"""
File Processor with streaming capabilities for PDF, DOCX, and text files.
"""
import asyncio
import aiofiles
import os
import magic
from typing import AsyncGenerator, Dict, Any, Optional, List
from pathlib import Path
import structlog
from dataclasses import dataclass

# Import file processing libraries
try:
    import PyPDF2
    from docx import Document
except ImportError:
    PyPDF2 = None
    Document = None

logger = structlog.get_logger(__name__)


@dataclass
class ProcessingResult:
    """File processing result."""
    success: bool
    text: str = ""
    metadata: Dict[str, Any] = None
    error: Optional[str] = None
    file_type: str = ""
    file_size: int = 0
    processing_time: float = 0.0


class FileProcessor:
    """
    File processor with streaming capabilities and support for multiple formats.
    """
    
    SUPPORTED_EXTENSIONS = {
        '.pdf': 'application/pdf',
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.txt': 'text/plain',
        '.md': 'text/markdown',
        '.csv': 'text/csv',
        '.json': 'application/json',
        '.xml': 'application/xml',
        '.html': 'text/html',
        '.htm': 'text/html'
    }
    
    MAX_FILE_SIZE = 50 * 1024 * 1024  # 50MB
    CHUNK_SIZE = 8192  # 8KB chunks for streaming
    
    def __init__(self):
        self.processing_stats = {
            'total_files': 0,
            'successful_files': 0,
            'failed_files': 0,
            'total_size_processed': 0
        }
    
    async def process_file(
        self,
        file_path: str,
        file_type: str = "auto",
        stream: bool = False
    ) -> ProcessingResult:
        """
        Process a file and extract text content.
        
        Args:
            file_path: Path to the file
            file_type: File type hint ("auto" for auto-detection)
            stream: Whether to use streaming processing
        
        Returns:
            ProcessingResult with extracted text and metadata
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Validate file
            validation_result = await self._validate_file(file_path)
            if not validation_result['valid']:
                return ProcessingResult(
                    success=False,
                    error=validation_result['error'],
                    processing_time=asyncio.get_event_loop().time() - start_time
                )
            
            file_info = validation_result['file_info']
            
            # Detect file type if auto
            if file_type == "auto":
                file_type = await self._detect_file_type(file_path)
            
            # Process based on file type
            if stream and file_info['size'] > self.CHUNK_SIZE * 10:
                # Use streaming for large files
                text_chunks = []
                async for chunk in self._process_file_streaming(file_path, file_type):
                    text_chunks.append(chunk)
                extracted_text = ''.join(text_chunks)
            else:
                # Process entire file at once
                extracted_text = await self._process_file_complete(file_path, file_type)
            
            end_time = asyncio.get_event_loop().time()
            processing_time = end_time - start_time
            
            # Update stats
            self.processing_stats['total_files'] += 1
            self.processing_stats['successful_files'] += 1
            self.processing_stats['total_size_processed'] += file_info['size']
            
            return ProcessingResult(
                success=True,
                text=extracted_text,
                metadata={
                    'file_name': file_info['name'],
                    'file_size': file_info['size'],
                    'file_type': file_type,
                    'mime_type': file_info['mime_type'],
                    'character_count': len(extracted_text),
                    'word_count': len(extracted_text.split()) if extracted_text else 0,
                    'processing_method': 'streaming' if stream else 'complete'
                },
                file_type=file_type,
                file_size=file_info['size'],
                processing_time=processing_time
            )
            
        except Exception as e:
            self.processing_stats['total_files'] += 1
            self.processing_stats['failed_files'] += 1
            
            logger.error("File processing failed", file_path=file_path, error=str(e))
            
            return ProcessingResult(
                success=False,
                error=str(e),
                processing_time=asyncio.get_event_loop().time() - start_time
            )
    
    async def _validate_file(self, file_path: str) -> Dict[str, Any]:
        """Validate file before processing."""
        try:
            path = Path(file_path)
            
            if not path.exists():
                return {'valid': False, 'error': 'File does not exist'}
            
            if not path.is_file():
                return {'valid': False, 'error': 'Path is not a file'}
            
            file_size = path.stat().st_size
            
            if file_size == 0:
                return {'valid': False, 'error': 'File is empty'}
            
            if file_size > self.MAX_FILE_SIZE:
                return {
                    'valid': False,
                    'error': f'File too large ({file_size} bytes). Maximum size: {self.MAX_FILE_SIZE} bytes'
                }
            
            # Get MIME type
            mime_type = magic.from_file(file_path, mime=True) if magic else 'application/octet-stream'
            
            return {
                'valid': True,
                'file_info': {
                    'name': path.name,
                    'size': file_size,
                    'extension': path.suffix.lower(),
                    'mime_type': mime_type
                }
            }
            
        except Exception as e:
            return {'valid': False, 'error': f'File validation error: {str(e)}'}
    
    async def _detect_file_type(self, file_path: str) -> str:
        """Detect file type based on extension and content."""
        path = Path(file_path)
        extension = path.suffix.lower()
        
        # Map extension to file type
        type_mapping = {
            '.pdf': 'pdf',
            '.docx': 'docx',
            '.txt': 'text',
            '.md': 'text',
            '.csv': 'text',
            '.json': 'text',
            '.xml': 'text',
            '.html': 'text',
            '.htm': 'text'
        }
        
        return type_mapping.get(extension, 'text')
    
    async def _process_file_complete(self, file_path: str, file_type: str) -> str:
        """Process entire file at once."""
        if file_type == 'pdf':
            return await self._extract_pdf_text(file_path)
        elif file_type == 'docx':
            return await self._extract_docx_text(file_path)
        else:
            return await self._extract_text_file(file_path)
    
    async def _process_file_streaming(
        self,
        file_path: str,
        file_type: str
    ) -> AsyncGenerator[str, None]:
        """Process file in streaming chunks."""
        if file_type in ['pdf', 'docx']:
            # For binary formats, we need to process the entire file first
            # then yield the result in chunks
            text = await self._process_file_complete(file_path, file_type)
            
            # Yield text in chunks
            for i in range(0, len(text), self.CHUNK_SIZE):
                yield text[i:i + self.CHUNK_SIZE]
        else:
            # For text files, we can stream line by line
            async for chunk in self._stream_text_file(file_path):
                yield chunk
    
    async def _extract_pdf_text(self, file_path: str) -> str:
        """Extract text from PDF file."""
        if not PyPDF2:
            raise ImportError("PyPDF2 is required for PDF processing")
        
        text_parts = []
        
        try:
            # Run PDF processing in thread pool to avoid blocking
            def extract_pdf():
                with open(file_path, 'rb') as file:
                    pdf_reader = PyPDF2.PdfReader(file)
                    text = []
                    
                    for page_num, page in enumerate(pdf_reader.pages):
                        try:
                            page_text = page.extract_text()
                            if page_text.strip():
                                text.append(f"--- Page {page_num + 1} ---\n{page_text}\n")
                        except Exception as e:
                            logger.warning(f"Failed to extract text from page {page_num + 1}: {str(e)}")
                            continue
                    
                    return '\n'.join(text)
            
            loop = asyncio.get_event_loop()
            text = await loop.run_in_executor(None, extract_pdf)
            return text
            
        except Exception as e:
            raise Exception(f"PDF extraction failed: {str(e)}")
    
    async def _extract_docx_text(self, file_path: str) -> str:
        """Extract text from DOCX file."""
        if not Document:
            raise ImportError("python-docx is required for DOCX processing")
        
        try:
            # Run DOCX processing in thread pool
            def extract_docx():
                doc = Document(file_path)
                text_parts = []
                
                # Extract paragraphs
                for paragraph in doc.paragraphs:
                    if paragraph.text.strip():
                        text_parts.append(paragraph.text)
                
                # Extract tables
                for table in doc.tables:
                    for row in table.rows:
                        row_text = []
                        for cell in row.cells:
                            if cell.text.strip():
                                row_text.append(cell.text.strip())
                        if row_text:
                            text_parts.append(' | '.join(row_text))
                
                return '\n\n'.join(text_parts)
            
            loop = asyncio.get_event_loop()
            text = await loop.run_in_executor(None, extract_docx)
            return text
            
        except Exception as e:
            raise Exception(f"DOCX extraction failed: {str(e)}")
    
    async def _extract_text_file(self, file_path: str) -> str:
        """Extract text from plain text file."""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                return await file.read()
        except UnicodeDecodeError:
            # Try with different encoding
            try:
                async with aiofiles.open(file_path, 'r', encoding='latin-1') as file:
                    return await file.read()
            except Exception as e:
                raise Exception(f"Text file extraction failed: {str(e)}")
    
    async def _stream_text_file(self, file_path: str) -> AsyncGenerator[str, None]:
        """Stream text file line by line."""
        try:
            async with aiofiles.open(file_path, 'r', encoding='utf-8', errors='ignore') as file:
                buffer = ""
                async for line in file:
                    buffer += line
                    
                    # Yield chunks when buffer reaches chunk size
                    while len(buffer) >= self.CHUNK_SIZE:
                        yield buffer[:self.CHUNK_SIZE]
                        buffer = buffer[self.CHUNK_SIZE:]
                
                # Yield remaining buffer
                if buffer:
                    yield buffer
                    
        except Exception as e:
            logger.error("Streaming text file failed", file_path=file_path, error=str(e))
            raise
    
    def get_supported_formats(self) -> List[Dict[str, str]]:
        """Get list of supported file formats."""
        formats = []
        for ext, mime_type in self.SUPPORTED_EXTENSIONS.items():
            formats.append({
                'extension': ext,
                'mime_type': mime_type,
                'description': self._get_format_description(ext)
            })
        return formats
    
    def _get_format_description(self, extension: str) -> str:
        """Get human-readable description for file format."""
        descriptions = {
            '.pdf': 'Portable Document Format',
            '.docx': 'Microsoft Word Document',
            '.txt': 'Plain Text File',
            '.md': 'Markdown File',
            '.csv': 'Comma-Separated Values',
            '.json': 'JSON Data File',
            '.xml': 'XML Document',
            '.html': 'HTML Document',
            '.htm': 'HTML Document'
        }
        return descriptions.get(extension, 'Unknown Format')
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get file processing statistics."""
        stats = self.processing_stats.copy()
        
        if stats['total_files'] > 0:
            stats['success_rate'] = stats['successful_files'] / stats['total_files']
            stats['failure_rate'] = stats['failed_files'] / stats['total_files']
            stats['avg_file_size'] = stats['total_size_processed'] / stats['successful_files'] if stats['successful_files'] > 0 else 0
        else:
            stats['success_rate'] = 0.0
            stats['failure_rate'] = 0.0
            stats['avg_file_size'] = 0
        
        return stats


# Global file processor instance
file_processor = FileProcessor()


# Convenience function for external use
async def process_file(
    file_path: str,
    file_type: str = "auto",
    stream: bool = False
) -> str:
    """
    Process a file and return extracted text.
    
    Args:
        file_path: Path to the file
        file_type: File type hint
        stream: Whether to use streaming
    
    Returns:
        Extracted text content
    """
    result = await file_processor.process_file(file_path, file_type, stream)
    
    if not result.success:
        raise Exception(result.error)
    
    return result.text
