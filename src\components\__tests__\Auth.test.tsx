import { describe, it, expect, vi, beforeEach } from 'vitest';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import userEvent from '@testing-library/user-event';
import Auth from '../Auth';
import { apiClient } from '../../lib/api';

// Mock the API client
vi.mock('../../lib/api', () => ({
  apiClient: {
    login: vi.fn(),
    register: vi.fn(),
  },
}));

describe('Auth Component', () => {
  const mockOnAuthSuccess = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  it('renders login form by default', () => {
    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    expect(screen.getByText('Welcome back')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your username')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your password')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /sign in/i })).toBeInTheDocument();
  });

  it('switches to registration form when clicking sign up', async () => {
    const user = userEvent.setup();
    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    await user.click(screen.getByText('Sign up'));
    
    expect(screen.getByText('Create your account')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your email')).toBeInTheDocument();
    expect(screen.getByPlaceholderText('Enter your full name')).toBeInTheDocument();
    expect(screen.getByRole('button', { name: /create account/i })).toBeInTheDocument();
  });

  it('toggles password visibility', async () => {
    const user = userEvent.setup();
    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    const passwordInput = screen.getByPlaceholderText('Enter your password');
    const toggleButton = screen.getByRole('button', { name: '' }); // Eye icon button
    
    expect(passwordInput).toHaveAttribute('type', 'password');
    
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'text');
    
    await user.click(toggleButton);
    expect(passwordInput).toHaveAttribute('type', 'password');
  });

  it('handles successful login', async () => {
    const user = userEvent.setup();
    const mockLoginResponse = {
      access_token: 'test-token',
      refresh_token: 'test-refresh',
      user: { id: 1, username: 'testuser' },
    };

    vi.mocked(apiClient.login).mockResolvedValue(mockLoginResponse);

    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    await user.type(screen.getByPlaceholderText('Enter your username'), 'testuser');
    await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(apiClient.login).toHaveBeenCalledWith({
        username: 'testuser',
        password: 'password123',
      });
      expect(mockOnAuthSuccess).toHaveBeenCalled();
    });
  });

  it('handles login error', async () => {
    const user = userEvent.setup();
    const mockError = new Error('Invalid credentials');
    mockError.response = { data: { detail: 'Invalid username or password' } };

    vi.mocked(apiClient.login).mockRejectedValue(mockError);

    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    await user.type(screen.getByPlaceholderText('Enter your username'), 'testuser');
    await user.type(screen.getByPlaceholderText('Enter your password'), 'wrongpassword');
    await user.click(screen.getByRole('button', { name: /sign in/i }));

    await waitFor(() => {
      expect(screen.getByText('Invalid username or password')).toBeInTheDocument();
      expect(mockOnAuthSuccess).not.toHaveBeenCalled();
    });
  });

  it('handles successful registration', async () => {
    const user = userEvent.setup();
    const mockRegisterResponse = {
      access_token: 'test-token',
      refresh_token: 'test-refresh',
      user: { id: 1, username: 'newuser', email: '<EMAIL>' },
    };

    vi.mocked(apiClient.register).mockResolvedValue(mockRegisterResponse);

    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    // Switch to registration
    await user.click(screen.getByText('Sign up'));
    
    // Fill form
    await user.type(screen.getByPlaceholderText('Enter your username'), 'newuser');
    await user.type(screen.getByPlaceholderText('Enter your email'), '<EMAIL>');
    await user.type(screen.getByPlaceholderText('Enter your full name'), 'Test User');
    await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
    
    await user.click(screen.getByRole('button', { name: /create account/i }));

    await waitFor(() => {
      expect(apiClient.register).toHaveBeenCalledWith({
        username: 'newuser',
        email: '<EMAIL>',
        full_name: 'Test User',
        password: 'password123',
      });
      expect(mockOnAuthSuccess).toHaveBeenCalled();
    });
  });

  it('validates required fields', async () => {
    const user = userEvent.setup();
    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    // Try to submit without filling fields
    await user.click(screen.getByRole('button', { name: /sign in/i }));
    
    // Form should not submit (browser validation)
    expect(apiClient.login).not.toHaveBeenCalled();
  });

  it('shows loading state during authentication', async () => {
    const user = userEvent.setup();
    
    // Mock a delayed response
    vi.mocked(apiClient.login).mockImplementation(
      () => new Promise(resolve => setTimeout(resolve, 100))
    );

    render(<Auth onAuthSuccess={mockOnAuthSuccess} />);
    
    await user.type(screen.getByPlaceholderText('Enter your username'), 'testuser');
    await user.type(screen.getByPlaceholderText('Enter your password'), 'password123');
    
    const submitButton = screen.getByRole('button', { name: /sign in/i });
    await user.click(submitButton);

    // Should show loading state
    expect(screen.getByText('Signing in...')).toBeInTheDocument();
    expect(submitButton).toBeDisabled();
  });
});
