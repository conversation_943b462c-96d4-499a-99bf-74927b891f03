/**
 * Main chat window component
 */
import React, { useEffect, useRef, useState } from 'react';
import { Send, Paperclip, Settings, MoreVertical } from 'lucide-react';
import { useChatStore } from '../store/chatStore';
import { useNativeWebSocket } from '../hooks/useWebSocket';
import { apiClient } from '../lib/api';
import MessageComponent from './Message';
import FileUpload from './FileUpload';

interface ChatWindowProps {
  className?: string;
}

const ChatWindow: React.FC<ChatWindowProps> = ({ className = '' }) => {
  const {
    currentSession,
    messages,
    isLoading,
    isConnected,
    setMessages,
    addMessage,
    setIsLoading
  } = useChatStore();

  const { sendMessage } = useNativeWebSocket();
  const [inputMessage, setInputMessage] = useState('');
  const [showFileUpload, setShowFileUpload] = useState(false);
  const [showSettings, setShowSettings] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  // Auto-scroll to bottom when new messages arrive
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  // Load messages when session changes
  useEffect(() => {
    if (currentSession) {
      loadMessages();
    } else {
      setMessages([]);
    }
  }, [currentSession, setMessages]);

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [inputMessage]);

  const loadMessages = async () => {
    if (!currentSession) return;

    try {
      setIsLoading(true);
      const sessionMessages = await apiClient.getMessages(currentSession.id);
      setMessages(sessionMessages);
    } catch (error) {
      console.error('Failed to load messages:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSendMessage = async () => {
    if (!inputMessage.trim() || !currentSession || !isConnected) return;

    const messageContent = inputMessage.trim();
    setInputMessage('');

    // Add user message immediately
    const userMessage = {
      id: Date.now(), // Temporary ID
      content: messageContent,
      message_type: 'user' as const,
      status: 'completed' as const,
      created_at: new Date().toISOString()
    };

    addMessage(userMessage);

    // Send via WebSocket
    const success = sendMessage({
      session_id: currentSession.id,
      message: messageContent,
      file_attachments: [],
      user_preferences: {}
    });

    if (!success) {
      console.error('Failed to send message via WebSocket');
      // You could show an error message here
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const handleFileUpload = (files: File[]) => {
    // Handle file upload logic here
    console.log('Files uploaded:', files);
    setShowFileUpload(false);
  };

  if (!currentSession) {
    return (
      <div className={`flex items-center justify-center h-full bg-gray-50 ${className}`}>
        <div className="text-center">
          <div className="text-6xl mb-4">💬</div>
          <h2 className="text-2xl font-semibold text-gray-700 mb-2">
            Welcome to NextNow
          </h2>
          <p className="text-gray-500">
            Select a chat session or create a new one to get started
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className={`flex flex-col h-full bg-white ${className}`}>
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-gray-200">
        <div className="flex items-center space-x-3">
          <div>
            <h1 className="text-lg font-semibold text-gray-900">
              {currentSession.title}
            </h1>
            <div className="flex items-center space-x-2 text-sm text-gray-500">
              <span>{currentSession.model_name}</span>
              <span>•</span>
              <span className={`inline-flex items-center ${
                isConnected ? 'text-green-600' : 'text-red-600'
              }`}>
                <div className={`w-2 h-2 rounded-full mr-1 ${
                  isConnected ? 'bg-green-500' : 'bg-red-500'
                }`} />
                {isConnected ? 'Connected' : 'Disconnected'}
              </span>
            </div>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={() => setShowSettings(!showSettings)}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100"
          >
            <Settings className="w-5 h-5" />
          </button>
          <button className="p-2 text-gray-400 hover:text-gray-600 rounded-lg hover:bg-gray-100">
            <MoreVertical className="w-5 h-5" />
          </button>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        {isLoading && messages.length === 0 ? (
          <div className="flex items-center justify-center h-full">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <>
            {messages.map((message) => (
              <MessageComponent key={message.id} message={message} />
            ))}
            <div ref={messagesEndRef} />
          </>
        )}
      </div>

      {/* Input Area */}
      <div className="border-t border-gray-200 p-4">
        <div className="flex items-end space-x-3">
          <div className="flex-1">
            <div className="relative">
              <textarea
                ref={textareaRef}
                value={inputMessage}
                onChange={(e) => setInputMessage(e.target.value)}
                onKeyPress={handleKeyPress}
                placeholder="Type your message..."
                className="w-full resize-none rounded-lg border border-gray-300 px-4 py-3 pr-12 focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                rows={1}
                style={{ maxHeight: '120px' }}
                disabled={!isConnected}
              />
              <button
                onClick={() => setShowFileUpload(!showFileUpload)}
                className="absolute right-3 top-3 p-1 text-gray-400 hover:text-gray-600"
              >
                <Paperclip className="w-5 h-5" />
              </button>
            </div>
            
            {/* File Upload */}
            {showFileUpload && (
              <div className="mt-2">
                <FileUpload
                  onFilesSelected={handleFileUpload}
                  onClose={() => setShowFileUpload(false)}
                />
              </div>
            )}
          </div>
          
          <button
            onClick={handleSendMessage}
            disabled={!inputMessage.trim() || !isConnected}
            className="flex items-center justify-center w-10 h-10 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:bg-gray-300 disabled:cursor-not-allowed transition-colors"
          >
            <Send className="w-5 h-5" />
          </button>
        </div>
        
        {!isConnected && (
          <div className="mt-2 text-sm text-red-600 text-center">
            Connection lost. Trying to reconnect...
          </div>
        )}
      </div>

      {/* Settings Panel */}
      {showSettings && (
        <div className="absolute top-16 right-4 w-80 bg-white border border-gray-200 rounded-lg shadow-lg p-4 z-10">
          <h3 className="text-lg font-semibold mb-4">Chat Settings</h3>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Model
              </label>
              <select className="w-full border border-gray-300 rounded-md px-3 py-2">
                <option value="gpt-3.5-turbo">GPT-3.5 Turbo</option>
                <option value="gpt-4">GPT-4</option>
                <option value="claude-3-haiku">Claude 3 Haiku</option>
                <option value="claude-3-sonnet">Claude 3 Sonnet</option>
              </select>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Temperature: {currentSession.temperature}
              </label>
              <input
                type="range"
                min="0"
                max="2"
                step="0.1"
                value={currentSession.temperature}
                className="w-full"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Max Tokens
              </label>
              <input
                type="number"
                value={currentSession.max_tokens}
                className="w-full border border-gray-300 rounded-md px-3 py-2"
              />
            </div>
            
            <div className="flex justify-end space-x-2">
              <button
                onClick={() => setShowSettings(false)}
                className="px-4 py-2 text-gray-600 hover:text-gray-800"
              >
                Cancel
              </button>
              <button className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700">
                Save
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default ChatWindow;
