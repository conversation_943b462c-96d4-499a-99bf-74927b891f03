#!/bin/bash

# NextNow Test Runner Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
RUN_FRONTEND=true
RUN_BACKEND=true
RUN_INTEGRATION=false
COVERAGE=false
WATCH=false

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --frontend-only)
            RUN_FRONTEND=true
            RUN_BACKEND=false
            shift
            ;;
        --backend-only)
            RUN_FRONTEND=false
            RUN_BACKEND=true
            shift
            ;;
        --integration)
            RUN_INTEGRATION=true
            shift
            ;;
        --coverage)
            COVERAGE=true
            shift
            ;;
        --watch)
            WATCH=true
            shift
            ;;
        --help)
            echo "NextNow Test Runner"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --frontend-only    Run only frontend tests"
            echo "  --backend-only     Run only backend tests"
            echo "  --integration      Run integration tests"
            echo "  --coverage         Generate coverage reports"
            echo "  --watch           Run tests in watch mode"
            echo "  --help            Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🧪 Running NextNow tests..."

# Frontend tests
if [ "$RUN_FRONTEND" = true ]; then
    print_status "Running frontend tests..."
    
    if [ "$WATCH" = true ]; then
        npm run test -- --watch
    elif [ "$COVERAGE" = true ]; then
        npm run test -- --coverage
    else
        npm run test
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Frontend tests passed"
    else
        print_error "Frontend tests failed"
        exit 1
    fi
fi

# Backend tests
if [ "$RUN_BACKEND" = true ]; then
    print_status "Running backend tests..."
    
    cd backend
    
    if [ "$WATCH" = true ]; then
        pytest --watch
    elif [ "$COVERAGE" = true ]; then
        pytest --cov=app --cov-report=html --cov-report=term-missing
    else
        pytest
    fi
    
    if [ $? -eq 0 ]; then
        print_success "Backend tests passed"
    else
        print_error "Backend tests failed"
        exit 1
    fi
    
    cd ..
fi

# Integration tests
if [ "$RUN_INTEGRATION" = true ]; then
    print_status "Running integration tests..."
    
    # Start test environment
    docker-compose -f docker-compose.test.yml up -d
    
    # Wait for services to be ready
    sleep 30
    
    # Run integration tests
    npm run test:e2e
    
    # Cleanup
    docker-compose -f docker-compose.test.yml down
    
    if [ $? -eq 0 ]; then
        print_success "Integration tests passed"
    else
        print_error "Integration tests failed"
        exit 1
    fi
fi

print_success "🎉 All tests completed successfully!"

if [ "$COVERAGE" = true ]; then
    echo ""
    print_status "📊 Coverage reports generated:"
    echo "  • Frontend: coverage/index.html"
    echo "  • Backend: backend/htmlcov/index.html"
fi
