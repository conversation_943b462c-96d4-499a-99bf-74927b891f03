/**
 * API client for NextNow backend
 */
import axios, { AxiosInstance, AxiosResponse } from 'axios';
import { ChatSession, Message, FileUpload } from '../store/chatStore';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8000';

export interface LoginRequest {
  username: string;
  password: string;
}

export interface RegisterRequest {
  username: string;
  email: string;
  full_name?: string;
  password: string;
}

export interface TokenResponse {
  access_token: string;
  refresh_token: string;
  token_type: string;
  expires_in: number;
  user: {
    id: number;
    username: string;
    email: string;
    full_name?: string;
    is_active: boolean;
    avatar_url?: string;
    bio?: string;
    created_at: string;
    last_login?: string;
  };
}

export interface ChatSessionCreate {
  title?: string;
  description?: string;
  model_name?: string;
  system_prompt?: string;
  temperature?: string;
  max_tokens?: number;
}

class ApiClient {
  private client: AxiosInstance;
  private accessToken: string | null = null;
  private refreshToken: string | null = null;

  constructor() {
    this.client = axios.create({
      baseURL: API_BASE_URL,
      timeout: 30000,
      headers: {
        'Content-Type': 'application/json',
      },
    });

    // Load tokens from localStorage
    this.loadTokens();

    // Request interceptor to add auth token
    this.client.interceptors.request.use(
      (config) => {
        if (this.accessToken) {
          config.headers.Authorization = `Bearer ${this.accessToken}`;
        }
        return config;
      },
      (error) => Promise.reject(error)
    );

    // Response interceptor to handle token refresh
    this.client.interceptors.response.use(
      (response) => response,
      async (error) => {
        const originalRequest = error.config;

        if (error.response?.status === 401 && !originalRequest._retry) {
          originalRequest._retry = true;

          try {
            await this.refreshAccessToken();
            originalRequest.headers.Authorization = `Bearer ${this.accessToken}`;
            return this.client(originalRequest);
          } catch (refreshError) {
            this.logout();
            window.location.href = '/login';
            return Promise.reject(refreshError);
          }
        }

        return Promise.reject(error);
      }
    );
  }

  private loadTokens() {
    this.accessToken = localStorage.getItem('access_token');
    this.refreshToken = localStorage.getItem('refresh_token');
  }

  private saveTokens(accessToken: string, refreshToken: string) {
    this.accessToken = accessToken;
    this.refreshToken = refreshToken;
    localStorage.setItem('access_token', accessToken);
    localStorage.setItem('refresh_token', refreshToken);
  }

  private clearTokens() {
    this.accessToken = null;
    this.refreshToken = null;
    localStorage.removeItem('access_token');
    localStorage.removeItem('refresh_token');
  }

  // Auth methods
  async login(credentials: LoginRequest): Promise<TokenResponse> {
    const formData = new FormData();
    formData.append('username', credentials.username);
    formData.append('password', credentials.password);

    const response: AxiosResponse<TokenResponse> = await this.client.post(
      '/api/v1/auth/login',
      formData,
      {
        headers: {
          'Content-Type': 'application/x-www-form-urlencoded',
        },
      }
    );

    this.saveTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
  }

  async register(userData: RegisterRequest): Promise<TokenResponse> {
    const response: AxiosResponse<TokenResponse> = await this.client.post(
      '/api/v1/auth/register',
      userData
    );

    this.saveTokens(response.data.access_token, response.data.refresh_token);
    return response.data;
  }

  async refreshAccessToken(): Promise<void> {
    if (!this.refreshToken) {
      throw new Error('No refresh token available');
    }

    const response: AxiosResponse<TokenResponse> = await this.client.post(
      '/api/v1/auth/refresh',
      { refresh_token: this.refreshToken }
    );

    this.saveTokens(response.data.access_token, response.data.refresh_token);
  }

  async logout(): Promise<void> {
    try {
      await this.client.post('/api/v1/auth/logout');
    } catch (error) {
      // Ignore errors during logout
    } finally {
      this.clearTokens();
    }
  }

  async getCurrentUser() {
    const response = await this.client.get('/api/v1/auth/me');
    return response.data;
  }

  // Chat methods
  async getChatSessions(): Promise<ChatSession[]> {
    const response = await this.client.get('/api/v1/chat/sessions');
    return response.data;
  }

  async createChatSession(sessionData: ChatSessionCreate): Promise<ChatSession> {
    const response = await this.client.post('/api/v1/chat/sessions', sessionData);
    return response.data;
  }

  async getChatSession(sessionId: number): Promise<ChatSession> {
    const response = await this.client.get(`/api/v1/chat/sessions/${sessionId}`);
    return response.data;
  }

  async updateChatSession(sessionId: number, updates: Partial<ChatSessionCreate>): Promise<ChatSession> {
    const response = await this.client.put(`/api/v1/chat/sessions/${sessionId}`, updates);
    return response.data;
  }

  async deleteChatSession(sessionId: number): Promise<void> {
    await this.client.delete(`/api/v1/chat/sessions/${sessionId}`);
  }

  async getMessages(sessionId: number, limit = 100, offset = 0): Promise<Message[]> {
    const response = await this.client.get(
      `/api/v1/chat/sessions/${sessionId}/messages`,
      { params: { limit, offset } }
    );
    return response.data;
  }

  // File methods
  async uploadFile(file: File, sessionId?: number): Promise<FileUpload> {
    const formData = new FormData();
    formData.append('file', file);
    if (sessionId) {
      formData.append('session_id', sessionId.toString());
    }

    const response = await this.client.post('/api/v1/files/upload', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  }

  async getFiles(sessionId?: number): Promise<FileUpload[]> {
    const params = sessionId ? { session_id: sessionId } : {};
    const response = await this.client.get('/api/v1/files/', { params });
    return response.data;
  }

  async getFile(fileId: number): Promise<FileUpload> {
    const response = await this.client.get(`/api/v1/files/${fileId}`);
    return response.data;
  }

  async deleteFile(fileId: number): Promise<void> {
    await this.client.delete(`/api/v1/files/${fileId}`);
  }

  async downloadFile(fileId: number): Promise<Blob> {
    const response = await this.client.get(`/api/v1/files/${fileId}/download`, {
      responseType: 'blob',
    });
    return response.data;
  }

  // Health check
  async healthCheck() {
    const response = await this.client.get('/api/v1/health/');
    return response.data;
  }

  // WebSocket URL
  getWebSocketUrl(): string {
    const wsProtocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
    const wsHost = API_BASE_URL.replace(/^https?:\/\//, '');
    return `${wsProtocol}//${wsHost}/api/v1/chat/ws/${this.accessToken}`;
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.accessToken;
  }

  // Get access token
  getAccessToken(): string | null {
    return this.accessToken;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
