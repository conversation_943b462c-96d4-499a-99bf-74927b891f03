# NextNow - AI-Powered Chat Platform

NextNow is a production-ready AI chat platform featuring dynamic model routing, intelligent tool integration, and advanced file processing capabilities. Built with modern technologies and designed for scalability.

## 🚀 Features

### Core Features
- **Dynamic AI Model Routing**: Intelligent selection between GPT-4, <PERSON>, and other models based on query complexity and user preferences
- **Real-time Chat**: WebSocket-powered instant messaging with typing indicators and connection status
- **File Processing**: Upload and process PDF, DOCX, TXT, and other document formats with streaming support
- **Tool Registry**: Extensible system for integrating various tools and APIs with fallback mechanisms
- **Session Management**: Persistent chat sessions with full conversation history

### Technical Features
- **Authentication**: JWT-based authentication with refresh tokens
- **Database**: PostgreSQL with SQLAlchemy ORM and Alembic migrations
- **Caching**: Redis for session management and performance optimization
- **Monitoring**: Prometheus metrics with Grafana dashboards
- **Background Tasks**: Celery for asynchronous file processing
- **API Documentation**: Auto-generated OpenAPI/Swagger documentation

## 🏗️ Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (React)       │◄──►│   (FastAPI)     │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ • React 18      │    │ • FastAPI       │    │ • PostgreSQL 15 │
│ • TypeScript    │    │ • Python 3.11   │    │ • Redis         │
│ • Tailwind CSS │    │ • SQLAlchemy    │    │                 │
│ • Zustand       │    │ • LiteLLM       │    │                 │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Services      │
                    │                 │
                    │ • Nginx         │
                    │ • Prometheus    │
                    │ • Grafana       │
                    │ • Celery        │
                    └─────────────────┘
```

## 🛠️ Technology Stack

### Frontend
- **React 18** with TypeScript
- **Vite** for build tooling
- **Tailwind CSS** for styling
- **Zustand** for state management
- **React Query** for server state
- **Socket.IO** for real-time communication

### Backend
- **FastAPI** with Python 3.11
- **SQLAlchemy** ORM with PostgreSQL
- **Redis** for caching and sessions
- **LiteLLM** for multi-model AI routing
- **Celery** for background tasks
- **Prometheus** for metrics

### Infrastructure
- **Docker** & Docker Compose
- **Nginx** reverse proxy
- **GitHub Actions** CI/CD
- **Grafana** monitoring dashboards

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Node.js 18+ (for local development)
- Python 3.11+ (for local development)

### Environment Setup

1. **Clone the repository**
   ```bash
   git clone https://github.com/your-org/nextnow.git
   cd nextnow
   ```

2. **Set up environment variables**
   ```bash
   # Backend
   cp backend/.env.example backend/.env
   
   # Frontend
   cp .env.example .env
   
   # Edit the files with your API keys and configuration
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Access the application**
   - Frontend: http://localhost:3000
   - Backend API: http://localhost:8000
   - API Documentation: http://localhost:8000/docs
   - Grafana: http://localhost:3001 (admin/admin)
   - Prometheus: http://localhost:9090

### Local Development

#### Backend Development
```bash
cd backend

# Install dependencies
pip install -r requirements.txt

# Run database migrations
alembic upgrade head

# Start development server
uvicorn app.main:app --reload --host 0.0.0.0 --port 8000
```

#### Frontend Development
```bash
# Install dependencies
npm install

# Start development server
npm run dev
```

## 📁 Project Structure

```
nextnow/
├── backend/                 # FastAPI backend
│   ├── app/
│   │   ├── agents/         # AI routing and agents
│   │   ├── api/            # API endpoints
│   │   ├── core/           # Core configuration
│   │   ├── models/         # Database models
│   │   └── tools/          # Tool registry and processors
│   ├── requirements.txt
│   └── Dockerfile
├── src/                    # React frontend
│   ├── components/         # React components
│   ├── hooks/              # Custom hooks
│   ├── lib/                # Utilities and API client
│   └── store/              # State management
├── nginx/                  # Nginx configuration
├── monitoring/             # Prometheus & Grafana config
├── .github/workflows/      # CI/CD pipelines
└── docker-compose.yml      # Multi-service setup
```

## 🔧 Configuration

### Backend Configuration
Key environment variables in `backend/.env`:

```env
# Database
DATABASE_URL=postgresql://nextnow:nextnow@localhost:5432/nextnow

# AI Models
OPENAI_API_KEY=your-openai-key
ANTHROPIC_API_KEY=your-anthropic-key
DEFAULT_MODEL=gpt-3.5-turbo

# Security
SECRET_KEY=your-secret-key
ACCESS_TOKEN_EXPIRE_MINUTES=30

# File Upload
MAX_FILE_SIZE=52428800  # 50MB
ALLOWED_FILE_TYPES=.pdf,.docx,.txt,.md
```

### Frontend Configuration
Key environment variables in `.env`:

```env
VITE_API_BASE_URL=http://localhost:8000
VITE_GA_MEASUREMENT_ID=G-XXXXXXXXXX
```

## 🧪 Testing

### Backend Tests
```bash
cd backend
pytest --cov=app --cov-report=html
```

### Frontend Tests
```bash
npm run test
npm run test:ui  # Vitest UI
```

### End-to-End Tests
```bash
npm run test:e2e
```

## 📊 Monitoring

The application includes comprehensive monitoring:

- **Health Checks**: `/api/v1/health/` endpoint
- **Metrics**: Prometheus metrics at `/metrics`
- **Dashboards**: Pre-configured Grafana dashboards
- **Logging**: Structured JSON logging with correlation IDs

## 🚀 Deployment

### Production Deployment

1. **Set production environment variables**
2. **Build and deploy with Docker Compose**
   ```bash
   docker-compose -f docker-compose.prod.yml up -d
   ```

### Cloud Deployment
- **AWS**: Use ECS/EKS with RDS and ElastiCache
- **GCP**: Use Cloud Run with Cloud SQL and Memorystore
- **Azure**: Use Container Instances with Azure Database

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

### Development Guidelines
- Follow the existing code style
- Add tests for new features
- Update documentation as needed
- Ensure CI/CD passes

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

- **Documentation**: [docs.nextnow.dev](https://docs.nextnow.dev)
- **Issues**: [GitHub Issues](https://github.com/your-org/nextnow/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/nextnow/discussions)

## 🙏 Acknowledgments

- OpenAI for GPT models
- Anthropic for Claude models
- FastAPI and React communities
- All contributors and supporters
