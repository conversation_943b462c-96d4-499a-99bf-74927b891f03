#!/bin/bash

# NextNow Production Deployment Script
set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Default values
ENVIRONMENT="production"
SKIP_TESTS=false
SKIP_BUILD=false
BACKUP_DB=true

# Parse command line arguments
while [[ $# -gt 0 ]]; do
    case $1 in
        --env)
            ENVIRONMENT="$2"
            shift 2
            ;;
        --skip-tests)
            SKIP_TESTS=true
            shift
            ;;
        --skip-build)
            SKIP_BUILD=true
            shift
            ;;
        --no-backup)
            BACKUP_DB=false
            shift
            ;;
        --help)
            echo "NextNow Production Deployment Script"
            echo ""
            echo "Usage: $0 [options]"
            echo ""
            echo "Options:"
            echo "  --env ENV         Target environment (default: production)"
            echo "  --skip-tests      Skip running tests before deployment"
            echo "  --skip-build      Skip building Docker images"
            echo "  --no-backup       Skip database backup"
            echo "  --help           Show this help message"
            exit 0
            ;;
        *)
            print_error "Unknown option: $1"
            exit 1
            ;;
    esac
done

print_status "🚀 Starting NextNow deployment to $ENVIRONMENT..."

# Check if we're on the main branch for production
if [ "$ENVIRONMENT" = "production" ]; then
    CURRENT_BRANCH=$(git branch --show-current)
    if [ "$CURRENT_BRANCH" != "main" ]; then
        print_error "Production deployments must be from the main branch. Current branch: $CURRENT_BRANCH"
        exit 1
    fi
    
    # Check if working directory is clean
    if [ -n "$(git status --porcelain)" ]; then
        print_error "Working directory is not clean. Please commit or stash changes."
        exit 1
    fi
fi

# Run tests unless skipped
if [ "$SKIP_TESTS" = false ]; then
    print_status "Running tests..."
    ./scripts/test.sh --coverage
    print_success "Tests passed"
fi

# Create backup if requested
if [ "$BACKUP_DB" = true ] && [ "$ENVIRONMENT" = "production" ]; then
    print_status "Creating database backup..."
    
    BACKUP_FILE="backup_$(date +%Y%m%d_%H%M%S).sql"
    
    # This would be customized based on your deployment setup
    # docker-compose exec postgres pg_dump -U nextnow nextnow > "backups/$BACKUP_FILE"
    
    print_success "Database backup created: $BACKUP_FILE"
fi

# Build Docker images unless skipped
if [ "$SKIP_BUILD" = false ]; then
    print_status "Building Docker images..."
    
    # Tag with git commit hash
    GIT_HASH=$(git rev-parse --short HEAD)
    
    # Build backend
    docker build -t nextnow/backend:$GIT_HASH -t nextnow/backend:latest ./backend
    
    # Build frontend
    docker build -t nextnow/frontend:$GIT_HASH -t nextnow/frontend:latest -f Dockerfile.frontend .
    
    print_success "Docker images built"
    
    # Push to registry if in production
    if [ "$ENVIRONMENT" = "production" ]; then
        print_status "Pushing images to registry..."
        
        docker push nextnow/backend:$GIT_HASH
        docker push nextnow/backend:latest
        docker push nextnow/frontend:$GIT_HASH
        docker push nextnow/frontend:latest
        
        print_success "Images pushed to registry"
    fi
fi

# Deploy based on environment
case $ENVIRONMENT in
    "production")
        print_status "Deploying to production..."
        
        # Update production environment
        docker-compose -f docker-compose.prod.yml pull
        docker-compose -f docker-compose.prod.yml up -d --remove-orphans
        
        # Run database migrations
        docker-compose -f docker-compose.prod.yml exec backend alembic upgrade head
        
        # Health check
        print_status "Performing health check..."
        sleep 30
        
        HEALTH_CHECK=$(curl -s -o /dev/null -w "%{http_code}" http://localhost/api/v1/health/)
        if [ "$HEALTH_CHECK" = "200" ]; then
            print_success "Health check passed"
        else
            print_error "Health check failed (HTTP $HEALTH_CHECK)"
            exit 1
        fi
        ;;
        
    "staging")
        print_status "Deploying to staging..."
        
        docker-compose -f docker-compose.staging.yml up -d --remove-orphans
        docker-compose -f docker-compose.staging.yml exec backend alembic upgrade head
        ;;
        
    "development")
        print_status "Deploying to development..."
        
        docker-compose up -d --remove-orphans
        docker-compose exec backend alembic upgrade head
        ;;
        
    *)
        print_error "Unknown environment: $ENVIRONMENT"
        exit 1
        ;;
esac

print_success "🎉 Deployment to $ENVIRONMENT completed successfully!"

# Show deployment info
echo ""
print_status "📋 Deployment Information:"
echo "  • Environment: $ENVIRONMENT"
echo "  • Git Hash: $(git rev-parse --short HEAD)"
echo "  • Deployed at: $(date)"

if [ "$ENVIRONMENT" = "production" ]; then
    echo "  • Frontend: https://nextnow.dev"
    echo "  • API: https://api.nextnow.dev"
    echo "  • Monitoring: https://monitoring.nextnow.dev"
elif [ "$ENVIRONMENT" = "staging" ]; then
    echo "  • Frontend: https://staging.nextnow.dev"
    echo "  • API: https://api-staging.nextnow.dev"
else
    echo "  • Frontend: http://localhost:3000"
    echo "  • API: http://localhost:8000"
    echo "  • Monitoring: http://localhost:3001"
fi

echo ""
print_status "🔍 Post-deployment checklist:"
echo "  □ Verify application is accessible"
echo "  □ Check logs for errors"
echo "  □ Test critical user flows"
echo "  □ Monitor system metrics"
echo "  □ Update documentation if needed"
