"""
Health check and monitoring API endpoints.
"""
import time
import psutil
from datetime import datetime
from typing import Dict, Any
from fastapi import APIRouter, Depends
from sqlalchemy.orm import Session
from sqlalchemy import text
import structlog

from ..core.database import get_db, get_redis
from ..core.config import settings
from ..tools.tool_registry import tool_registry
from ..agents.dynamic_router import router as ai_router

logger = structlog.get_logger(__name__)

router = APIRouter()


@router.get("/")
async def health_check():
    """Basic health check endpoint."""
    return {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.APP_VERSION
    }


@router.get("/detailed")
async def detailed_health_check(db: Session = Depends(get_db)):
    """Detailed health check with all system components."""
    health_data = {
        "status": "healthy",
        "timestamp": datetime.utcnow().isoformat(),
        "version": settings.APP_VERSION,
        "components": {}
    }
    
    overall_healthy = True
    
    # Database health
    try:
        start_time = time.time()
        db.execute(text("SELECT 1"))
        db_latency = time.time() - start_time
        
        health_data["components"]["database"] = {
            "status": "healthy",
            "latency_ms": round(db_latency * 1000, 2),
            "url": settings.DATABASE_URL.split("@")[-1] if "@" in settings.DATABASE_URL else "hidden"
        }
    except Exception as e:
        overall_healthy = False
        health_data["components"]["database"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Redis health
    try:
        redis_client = get_redis()
        start_time = time.time()
        redis_client.ping()
        redis_latency = time.time() - start_time
        
        redis_info = redis_client.info()
        
        health_data["components"]["redis"] = {
            "status": "healthy",
            "latency_ms": round(redis_latency * 1000, 2),
            "connected_clients": redis_info.get("connected_clients", 0),
            "used_memory_human": redis_info.get("used_memory_human", "unknown"),
            "uptime_in_seconds": redis_info.get("uptime_in_seconds", 0)
        }
    except Exception as e:
        overall_healthy = False
        health_data["components"]["redis"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # Tool registry health
    try:
        tool_health = tool_registry.health_check()
        
        health_data["components"]["tools"] = {
            "status": "healthy" if tool_health["available_tools"] > 0 else "degraded",
            "total_tools": tool_health["total_tools"],
            "available_tools": tool_health["available_tools"],
            "unavailable_tools": tool_health["unavailable_tools"],
            "tool_details": tool_health["tool_details"]
        }
        
        if tool_health["available_tools"] == 0:
            overall_healthy = False
            
    except Exception as e:
        overall_healthy = False
        health_data["components"]["tools"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # AI Router health
    try:
        # Check if router has models configured
        available_models = len(ai_router.models)
        
        health_data["components"]["ai_router"] = {
            "status": "healthy" if available_models > 0 else "degraded",
            "available_models": available_models,
            "performance_cache_size": len(ai_router.performance_cache),
            "fallback_chain_length": len(ai_router.fallback_chain)
        }
        
        if available_models == 0:
            overall_healthy = False
            
    except Exception as e:
        overall_healthy = False
        health_data["components"]["ai_router"] = {
            "status": "unhealthy",
            "error": str(e)
        }
    
    # System resources
    try:
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        health_data["components"]["system"] = {
            "status": "healthy",
            "cpu_percent": cpu_percent,
            "memory_percent": memory.percent,
            "memory_available_gb": round(memory.available / (1024**3), 2),
            "disk_percent": disk.percent,
            "disk_free_gb": round(disk.free / (1024**3), 2)
        }
        
        # Mark as degraded if resources are high
        if cpu_percent > 90 or memory.percent > 90 or disk.percent > 90:
            health_data["components"]["system"]["status"] = "degraded"
            
    except Exception as e:
        health_data["components"]["system"] = {
            "status": "unknown",
            "error": str(e)
        }
    
    # Update overall status
    health_data["status"] = "healthy" if overall_healthy else "unhealthy"
    
    return health_data


@router.get("/metrics")
async def get_metrics():
    """Get application metrics."""
    try:
        # System metrics
        cpu_percent = psutil.cpu_percent(interval=1)
        memory = psutil.virtual_memory()
        disk = psutil.disk_usage('/')
        
        # Tool metrics
        tool_stats = tool_registry.get_tool_stats()
        
        # Calculate aggregated tool metrics
        total_tool_calls = sum(stats.get("total_calls", 0) for stats in tool_stats.values())
        total_successful_calls = sum(stats.get("successful_calls", 0) for stats in tool_stats.values())
        total_failed_calls = sum(stats.get("failed_calls", 0) for stats in tool_stats.values())
        
        success_rate = (total_successful_calls / total_tool_calls) if total_tool_calls > 0 else 0
        
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "system": {
                "cpu_percent": cpu_percent,
                "memory_percent": memory.percent,
                "memory_used_gb": round((memory.total - memory.available) / (1024**3), 2),
                "memory_total_gb": round(memory.total / (1024**3), 2),
                "disk_percent": disk.percent,
                "disk_used_gb": round((disk.total - disk.free) / (1024**3), 2),
                "disk_total_gb": round(disk.total / (1024**3), 2)
            },
            "tools": {
                "total_calls": total_tool_calls,
                "successful_calls": total_successful_calls,
                "failed_calls": total_failed_calls,
                "success_rate": round(success_rate, 4),
                "available_tools": len([t for t in tool_registry.tools.values() if t.metadata.status.value == "available"]),
                "tool_stats": tool_stats
            },
            "ai_router": {
                "available_models": len(ai_router.models),
                "performance_cache": ai_router.performance_cache
            }
        }
        
    except Exception as e:
        logger.error("Failed to get metrics", error=str(e))
        return {
            "error": "Failed to collect metrics",
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/readiness")
async def readiness_check(db: Session = Depends(get_db)):
    """Readiness check for Kubernetes/container orchestration."""
    try:
        # Check database connection
        db.execute(text("SELECT 1"))
        
        # Check Redis connection
        redis_client = get_redis()
        redis_client.ping()
        
        # Check if essential tools are available
        essential_tools = ["file_processor"]  # Add more essential tools as needed
        for tool_name in essential_tools:
            tool = tool_registry.get_tool(tool_name)
            if not tool or tool.metadata.status.value != "available":
                raise Exception(f"Essential tool {tool_name} not available")
        
        return {
            "status": "ready",
            "timestamp": datetime.utcnow().isoformat()
        }
        
    except Exception as e:
        logger.error("Readiness check failed", error=str(e))
        return {
            "status": "not_ready",
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat()
        }


@router.get("/liveness")
async def liveness_check():
    """Liveness check for Kubernetes/container orchestration."""
    return {
        "status": "alive",
        "timestamp": datetime.utcnow().isoformat(),
        "uptime_seconds": time.time() - psutil.boot_time()
    }


@router.get("/version")
async def version_info():
    """Get version and build information."""
    return {
        "version": settings.APP_VERSION,
        "app_name": settings.APP_NAME,
        "environment": "development" if settings.DEBUG else "production",
        "python_version": "3.11+",  # Update based on your Python version
        "build_timestamp": datetime.utcnow().isoformat()
    }
