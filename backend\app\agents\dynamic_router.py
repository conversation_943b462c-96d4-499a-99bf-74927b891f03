"""
Dynamic Router for intelligent model selection and routing using LiteLLM.
"""
import asyncio
import time
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import litellm
from litellm import completion, acompletion
import structlog
from ..core.config import settings

logger = structlog.get_logger(__name__)


class ModelProvider(Enum):
    """Supported model providers."""
    OPENAI = "openai"
    ANTHROPIC = "anthropic"
    GOOGLE = "google"
    COHERE = "cohere"
    AZURE = "azure"


@dataclass
class ModelConfig:
    """Model configuration."""
    name: str
    provider: ModelProvider
    max_tokens: int
    cost_per_1k_tokens: float
    latency_score: float  # Lower is better
    quality_score: float  # Higher is better
    context_window: int
    supports_streaming: bool = True
    supports_function_calling: bool = False


@dataclass
class RoutingDecision:
    """Routing decision result."""
    model_name: str
    provider: ModelProvider
    confidence: float
    reasoning: str
    estimated_cost: float
    estimated_latency: float


class DynamicRouter:
    """
    Intelligent router that selects the best model based on:
    - Query complexity
    - Cost optimization
    - Latency requirements
    - Model capabilities
    - Fallback strategies
    """
    
    def __init__(self):
        self.models = self._initialize_models()
        self.performance_cache = {}
        self.fallback_chain = self._setup_fallback_chain()
        
        # Configure LiteLLM
        litellm.set_verbose = settings.DEBUG
        if settings.OPENAI_API_KEY:
            litellm.openai_key = settings.OPENAI_API_KEY
        if settings.ANTHROPIC_API_KEY:
            litellm.anthropic_key = settings.ANTHROPIC_API_KEY
    
    def _initialize_models(self) -> Dict[str, ModelConfig]:
        """Initialize available models with their configurations."""
        return {
            "gpt-4-turbo": ModelConfig(
                name="gpt-4-turbo",
                provider=ModelProvider.OPENAI,
                max_tokens=4096,
                cost_per_1k_tokens=0.03,
                latency_score=3.0,
                quality_score=9.5,
                context_window=128000,
                supports_function_calling=True
            ),
            "gpt-3.5-turbo": ModelConfig(
                name="gpt-3.5-turbo",
                provider=ModelProvider.OPENAI,
                max_tokens=4096,
                cost_per_1k_tokens=0.002,
                latency_score=1.5,
                quality_score=8.0,
                context_window=16385,
                supports_function_calling=True
            ),
            "claude-3-opus": ModelConfig(
                name="claude-3-opus-20240229",
                provider=ModelProvider.ANTHROPIC,
                max_tokens=4096,
                cost_per_1k_tokens=0.075,
                latency_score=4.0,
                quality_score=9.8,
                context_window=200000,
                supports_function_calling=False
            ),
            "claude-3-sonnet": ModelConfig(
                name="claude-3-sonnet-20240229",
                provider=ModelProvider.ANTHROPIC,
                max_tokens=4096,
                cost_per_1k_tokens=0.015,
                latency_score=2.5,
                quality_score=9.0,
                context_window=200000,
                supports_function_calling=False
            ),
            "claude-3-haiku": ModelConfig(
                name="claude-3-haiku-20240307",
                provider=ModelProvider.ANTHROPIC,
                max_tokens=4096,
                cost_per_1k_tokens=0.0025,
                latency_score=1.0,
                quality_score=7.5,
                context_window=200000,
                supports_function_calling=False
            )
        }
    
    def _setup_fallback_chain(self) -> List[str]:
        """Setup fallback chain for model failures."""
        return [
            "gpt-3.5-turbo",
            "claude-3-haiku",
            "gpt-4-turbo",
            "claude-3-sonnet"
        ]
    
    async def route_request(
        self,
        messages: List[Dict[str, Any]],
        user_preferences: Optional[Dict[str, Any]] = None,
        requirements: Optional[Dict[str, Any]] = None
    ) -> RoutingDecision:
        """
        Route request to the best model based on analysis.
        
        Args:
            messages: Chat messages
            user_preferences: User preferences (cost, speed, quality)
            requirements: Specific requirements (function_calling, etc.)
        
        Returns:
            RoutingDecision with selected model and reasoning
        """
        try:
            # Analyze query complexity
            complexity_score = await self._analyze_complexity(messages)
            
            # Get user preferences with defaults
            prefs = user_preferences or {}
            cost_weight = prefs.get("cost_priority", 0.3)
            speed_weight = prefs.get("speed_priority", 0.3)
            quality_weight = prefs.get("quality_priority", 0.4)
            
            # Filter models based on requirements
            available_models = self._filter_models_by_requirements(requirements or {})
            
            # Score each model
            best_model = None
            best_score = -1
            reasoning_parts = []
            
            for model_key, model in available_models.items():
                score = self._calculate_model_score(
                    model, complexity_score, cost_weight, speed_weight, quality_weight
                )
                
                if score > best_score:
                    best_score = score
                    best_model = model
            
            if not best_model:
                # Fallback to default
                best_model = self.models[settings.DEFAULT_MODEL]
                reasoning_parts.append("Using default model as fallback")
            
            # Estimate cost and latency
            estimated_cost = self._estimate_cost(messages, best_model)
            estimated_latency = self._estimate_latency(messages, best_model)
            
            reasoning = f"Selected {best_model.name} based on complexity={complexity_score:.2f}, " \
                       f"cost_weight={cost_weight}, speed_weight={speed_weight}, quality_weight={quality_weight}"
            
            return RoutingDecision(
                model_name=best_model.name,
                provider=best_model.provider,
                confidence=best_score,
                reasoning=reasoning,
                estimated_cost=estimated_cost,
                estimated_latency=estimated_latency
            )
            
        except Exception as e:
            logger.error("Error in route_request", error=str(e))
            # Fallback to default model
            default_model = self.models[settings.DEFAULT_MODEL]
            return RoutingDecision(
                model_name=default_model.name,
                provider=default_model.provider,
                confidence=0.5,
                reasoning=f"Fallback due to routing error: {str(e)}",
                estimated_cost=0.01,
                estimated_latency=2.0
            )
    
    async def _analyze_complexity(self, messages: List[Dict[str, Any]]) -> float:
        """Analyze query complexity (0-1 scale)."""
        if not messages:
            return 0.1
        
        # Get the latest user message
        user_messages = [msg for msg in messages if msg.get("role") == "user"]
        if not user_messages:
            return 0.1
        
        latest_message = user_messages[-1].get("content", "")
        
        # Simple complexity heuristics
        complexity_factors = []
        
        # Length factor
        length_factor = min(len(latest_message) / 1000, 1.0)
        complexity_factors.append(length_factor * 0.3)
        
        # Keyword complexity
        complex_keywords = [
            "analyze", "explain", "complex", "detailed", "comprehensive",
            "research", "compare", "evaluate", "synthesize", "reasoning"
        ]
        keyword_score = sum(1 for keyword in complex_keywords if keyword in latest_message.lower())
        keyword_factor = min(keyword_score / len(complex_keywords), 1.0)
        complexity_factors.append(keyword_factor * 0.4)
        
        # Question complexity
        question_marks = latest_message.count("?")
        question_factor = min(question_marks / 3, 1.0)
        complexity_factors.append(question_factor * 0.3)
        
        return sum(complexity_factors)
    
    def _filter_models_by_requirements(self, requirements: Dict[str, Any]) -> Dict[str, ModelConfig]:
        """Filter models based on requirements."""
        filtered = {}
        
        for key, model in self.models.items():
            # Check function calling requirement
            if requirements.get("function_calling") and not model.supports_function_calling:
                continue
            
            # Check context window requirement
            min_context = requirements.get("min_context_window", 0)
            if model.context_window < min_context:
                continue
            
            # Check streaming requirement
            if requirements.get("streaming") and not model.supports_streaming:
                continue
            
            filtered[key] = model
        
        return filtered if filtered else self.models
    
    def _calculate_model_score(
        self,
        model: ModelConfig,
        complexity: float,
        cost_weight: float,
        speed_weight: float,
        quality_weight: float
    ) -> float:
        """Calculate weighted score for model selection."""
        # Normalize scores (0-1)
        cost_score = 1.0 - (model.cost_per_1k_tokens / 0.1)  # Assuming max cost of $0.1
        speed_score = 1.0 - (model.latency_score / 5.0)  # Assuming max latency of 5
        quality_score = model.quality_score / 10.0  # Assuming max quality of 10
        
        # Adjust quality weight based on complexity
        adjusted_quality_weight = quality_weight + (complexity * 0.3)
        adjusted_cost_weight = cost_weight - (complexity * 0.15)
        adjusted_speed_weight = speed_weight - (complexity * 0.15)
        
        # Normalize weights
        total_weight = adjusted_quality_weight + adjusted_cost_weight + adjusted_speed_weight
        adjusted_quality_weight /= total_weight
        adjusted_cost_weight /= total_weight
        adjusted_speed_weight /= total_weight
        
        # Calculate weighted score
        score = (
            cost_score * adjusted_cost_weight +
            speed_score * adjusted_speed_weight +
            quality_score * adjusted_quality_weight
        )
        
        return max(0.0, min(1.0, score))
    
    def _estimate_cost(self, messages: List[Dict[str, Any]], model: ModelConfig) -> float:
        """Estimate cost for the request."""
        # Simple token estimation
        total_chars = sum(len(str(msg.get("content", ""))) for msg in messages)
        estimated_tokens = total_chars / 4  # Rough estimation
        return (estimated_tokens / 1000) * model.cost_per_1k_tokens
    
    def _estimate_latency(self, messages: List[Dict[str, Any]], model: ModelConfig) -> float:
        """Estimate latency for the request."""
        # Base latency + complexity factor
        total_chars = sum(len(str(msg.get("content", ""))) for msg in messages)
        complexity_factor = min(total_chars / 5000, 2.0)  # Max 2x multiplier
        return model.latency_score * (1 + complexity_factor)
    
    async def complete_with_fallback(
        self,
        messages: List[Dict[str, Any]],
        routing_decision: RoutingDecision,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Complete request with automatic fallback on failure.
        """
        models_to_try = [routing_decision.model_name] + [
            model for model in self.fallback_chain 
            if model != routing_decision.model_name
        ]
        
        last_error = None
        
        for model_name in models_to_try:
            try:
                start_time = time.time()
                
                response = await acompletion(
                    model=model_name,
                    messages=messages,
                    **kwargs
                )
                
                end_time = time.time()
                
                # Update performance cache
                self._update_performance_cache(
                    model_name, 
                    end_time - start_time,
                    success=True
                )
                
                return {
                    "response": response,
                    "model_used": model_name,
                    "latency": end_time - start_time,
                    "fallback_used": model_name != routing_decision.model_name
                }
                
            except Exception as e:
                last_error = e
                logger.warning(
                    "Model failed, trying fallback",
                    model=model_name,
                    error=str(e)
                )
                
                self._update_performance_cache(
                    model_name,
                    0,
                    success=False
                )
                
                continue
        
        # All models failed
        raise Exception(f"All models failed. Last error: {last_error}")
    
    def _update_performance_cache(self, model_name: str, latency: float, success: bool):
        """Update performance metrics for model."""
        if model_name not in self.performance_cache:
            self.performance_cache[model_name] = {
                "success_rate": 0.0,
                "avg_latency": 0.0,
                "total_requests": 0
            }
        
        cache = self.performance_cache[model_name]
        cache["total_requests"] += 1
        
        if success:
            # Update success rate
            current_successes = cache["success_rate"] * (cache["total_requests"] - 1)
            cache["success_rate"] = (current_successes + 1) / cache["total_requests"]
            
            # Update average latency
            current_total_latency = cache["avg_latency"] * (cache["total_requests"] - 1)
            cache["avg_latency"] = (current_total_latency + latency) / cache["total_requests"]
        else:
            # Update success rate only
            current_successes = cache["success_rate"] * (cache["total_requests"] - 1)
            cache["success_rate"] = current_successes / cache["total_requests"]


# Global router instance
router = DynamicRouter()
