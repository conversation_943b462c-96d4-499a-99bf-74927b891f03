/**
 * Main Chat application component
 */
import React, { useEffect, useState } from 'react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import SessionSidebar from './SessionSidebar';
import ChatWindow from './ChatWindow';
import { useChatStore } from '../store/chatStore';
import { apiClient } from '../lib/api';

// Create a client
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: 1,
      refetchOnWindowFocus: false,
    },
  },
});

interface User {
  id: number;
  username: string;
  email: string;
  full_name?: string;
  avatar_url?: string;
}

const Chat: React.FC = () => {
  const { sidebarOpen } = useChatStore();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check if user is authenticated
      if (!apiClient.isAuthenticated()) {
        setError('Not authenticated');
        setLoading(false);
        return;
      }

      // Get current user
      const currentUser = await apiClient.getCurrentUser();
      setUser(currentUser);

      // Health check
      try {
        await apiClient.healthCheck();
      } catch (healthError) {
        console.warn('Backend health check failed:', healthError);
        // Continue anyway - the app might still work
      }

    } catch (error) {
      console.error('Failed to initialize app:', error);
      setError('Failed to initialize application');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading NextNow...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="h-screen flex items-center justify-center bg-gray-50">
        <div className="text-center">
          <div className="text-red-500 text-6xl mb-4">⚠️</div>
          <h2 className="text-2xl font-semibold text-gray-900 mb-2">
            Something went wrong
          </h2>
          <p className="text-gray-600 mb-4">{error}</p>
          <button
            onClick={() => window.location.reload()}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            Reload Page
          </button>
        </div>
      </div>
    );
  }

  return (
    <QueryClientProvider client={queryClient}>
      <div className="h-screen flex bg-white">
        {/* Sidebar */}
        <SessionSidebar />
        
        {/* Main Chat Area */}
        <div className="flex-1 flex flex-col">
          <ChatWindow />
        </div>
        
        {/* User Info (optional overlay) */}
        {user && (
          <div className="absolute top-4 right-4 hidden lg:block">
            <div className="flex items-center space-x-2 bg-white border border-gray-200 rounded-lg px-3 py-2 shadow-sm">
              {user.avatar_url ? (
                <img
                  src={user.avatar_url}
                  alt={user.username}
                  className="w-6 h-6 rounded-full"
                />
              ) : (
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                  {user.username.charAt(0).toUpperCase()}
                </div>
              )}
              <span className="text-sm text-gray-700">{user.username}</span>
            </div>
          </div>
        )}
      </div>
    </QueryClientProvider>
  );
};

export default Chat;
