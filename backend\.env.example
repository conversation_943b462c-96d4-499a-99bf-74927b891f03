# Application Configuration
APP_NAME=NextNow API
APP_VERSION=1.0.0
DEBUG=false

# Server Configuration
HOST=0.0.0.0
PORT=8000

# Database Configuration
DATABASE_URL=postgresql://nextnow:nextnow@localhost:5432/nextnow
DATABASE_ECHO=false

# Redis Configuration
REDIS_URL=redis://localhost:6379/0

# Security Configuration
SECRET_KEY=your-secret-key-change-in-production-make-it-very-long-and-random
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# CORS Configuration
ALLOWED_ORIGINS=http://localhost:3000,http://localhost:5173,https://nextnow.dev

# File Upload Configuration
MAX_FILE_SIZE=52428800  # 50MB in bytes
UPLOAD_DIR=uploads
ALLOWED_FILE_TYPES=.pdf,.docx,.txt,.md,.csv,.json,.xml,.html,.htm

# AI Models Configuration
OPENAI_API_KEY=your-openai-api-key-here
ANTHROPIC_API_KEY=your-anthropic-api-key-here
DEFAULT_MODEL=gpt-3.5-turbo

# LiteLLM Configuration
LITELLM_MASTER_KEY=your-litellm-master-key
LITELLM_DATABASE_URL=postgresql://nextnow:nextnow@localhost:5432/litellm

# Monitoring Configuration
PROMETHEUS_PORT=8001

# Logging Configuration
LOG_LEVEL=INFO
LOG_FORMAT=json

# WebSocket Configuration
WS_HEARTBEAT_INTERVAL=30
WS_MAX_CONNECTIONS=1000

# Celery Configuration (for background tasks)
CELERY_BROKER_URL=redis://localhost:6379/1
CELERY_RESULT_BACKEND=redis://localhost:6379/2
