"""
File upload and processing API endpoints.
"""
import os
import uuid
from datetime import datetime
from typing import List, Optional
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, status, BackgroundTasks
from sqlalchemy.orm import Session
from pydantic import BaseModel
import structlog

from ..core.database import get_db
from ..core.security import get_current_active_user
from ..core.config import settings
from ..models.user import User
from ..models.chat import FileUpload
from ..tools.file_processor import file_processor

logger = structlog.get_logger(__name__)

router = APIRouter()


# Pydantic models
class FileUploadResponse(BaseModel):
    """File upload response model."""
    id: int
    filename: str
    original_filename: str
    file_size: int
    file_type: str
    mime_type: str
    is_processed: bool
    processing_status: str
    extracted_text: Optional[str]
    processing_error: Optional[str]
    created_at: datetime
    processed_at: Optional[datetime]

    class Config:
        from_attributes = True


class FileProcessingRequest(BaseModel):
    """File processing request model."""
    file_id: int
    force_reprocess: bool = False


# Helper functions
def get_file_upload(db: Session, file_id: int, user_id: int) -> Optional[FileUpload]:
    """Get file upload by ID and user."""
    return db.query(FileUpload).filter(
        FileUpload.id == file_id,
        FileUpload.user_id == user_id
    ).first()


def is_allowed_file_type(filename: str) -> bool:
    """Check if file type is allowed."""
    file_ext = os.path.splitext(filename)[1].lower()
    return file_ext in settings.ALLOWED_FILE_TYPES


def generate_unique_filename(original_filename: str) -> str:
    """Generate unique filename while preserving extension."""
    file_ext = os.path.splitext(original_filename)[1]
    unique_id = str(uuid.uuid4())
    return f"{unique_id}{file_ext}"


async def save_uploaded_file(upload_file: UploadFile, filename: str) -> str:
    """Save uploaded file to disk."""
    file_path = os.path.join(settings.UPLOAD_DIR, filename)
    
    # Ensure upload directory exists
    os.makedirs(settings.UPLOAD_DIR, exist_ok=True)
    
    # Save file
    with open(file_path, "wb") as buffer:
        content = await upload_file.read()
        buffer.write(content)
    
    return file_path


async def process_file_background(file_id: int, db_session_factory):
    """Background task to process uploaded file."""
    db = db_session_factory()
    
    try:
        file_upload = db.query(FileUpload).filter(FileUpload.id == file_id).first()
        
        if not file_upload:
            logger.error("File upload not found for processing", file_id=file_id)
            return
        
        # Update processing status
        file_upload.processing_status = "processing"
        db.commit()
        
        # Process file
        result = await file_processor.process_file(
            file_upload.file_path,
            file_upload.file_type,
            stream=False
        )
        
        if result.success:
            file_upload.extracted_text = result.text
            file_upload.is_processed = True
            file_upload.processing_status = "completed"
            file_upload.processed_at = datetime.utcnow()
            
            # Update metadata
            if result.metadata:
                file_upload.metadata = result.metadata
            
            logger.info(
                "File processed successfully",
                file_id=file_id,
                file_size=result.file_size,
                processing_time=result.processing_time
            )
        else:
            file_upload.processing_status = "failed"
            file_upload.processing_error = result.error
            
            logger.error(
                "File processing failed",
                file_id=file_id,
                error=result.error
            )
        
        db.commit()
        
    except Exception as e:
        logger.error("Background file processing failed", file_id=file_id, error=str(e))
        
        # Update error status
        file_upload = db.query(FileUpload).filter(FileUpload.id == file_id).first()
        if file_upload:
            file_upload.processing_status = "failed"
            file_upload.processing_error = str(e)
            db.commit()
    
    finally:
        db.close()


# API endpoints
@router.post("/upload", response_model=FileUploadResponse, status_code=status.HTTP_201_CREATED)
async def upload_file(
    background_tasks: BackgroundTasks,
    file: UploadFile = File(...),
    session_id: Optional[int] = None,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Upload a file for processing."""
    logger.info("File upload started", filename=file.filename, user_id=current_user.id)
    
    # Validate file
    if not file.filename:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="No file provided"
        )
    
    if not is_allowed_file_type(file.filename):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"File type not allowed. Allowed types: {', '.join(settings.ALLOWED_FILE_TYPES)}"
        )
    
    # Check file size
    file_content = await file.read()
    file_size = len(file_content)
    
    if file_size > settings.MAX_FILE_SIZE:
        raise HTTPException(
            status_code=status.HTTP_413_REQUEST_ENTITY_TOO_LARGE,
            detail=f"File too large. Maximum size: {settings.MAX_FILE_SIZE} bytes"
        )
    
    if file_size == 0:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Empty file"
        )
    
    # Reset file pointer
    await file.seek(0)
    
    try:
        # Generate unique filename
        unique_filename = generate_unique_filename(file.filename)
        
        # Save file
        file_path = await save_uploaded_file(file, unique_filename)
        
        # Create database record
        file_upload = FileUpload(
            user_id=current_user.id,
            session_id=session_id,
            filename=unique_filename,
            original_filename=file.filename,
            file_path=file_path,
            file_size=file_size,
            file_type=os.path.splitext(file.filename)[1].lower(),
            mime_type=file.content_type or "application/octet-stream",
            processing_status="pending"
        )
        
        db.add(file_upload)
        db.commit()
        db.refresh(file_upload)
        
        # Start background processing
        from ..core.database import SessionLocal
        background_tasks.add_task(
            process_file_background,
            file_upload.id,
            SessionLocal
        )
        
        logger.info(
            "File uploaded successfully",
            file_id=file_upload.id,
            filename=file.filename,
            file_size=file_size,
            user_id=current_user.id
        )
        
        return FileUploadResponse.from_orm(file_upload)
        
    except Exception as e:
        logger.error("File upload failed", filename=file.filename, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File upload failed"
        )


@router.get("/", response_model=List[FileUploadResponse])
async def get_files(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    session_id: Optional[int] = None,
    limit: int = 50,
    offset: int = 0
):
    """Get user's uploaded files."""
    query = db.query(FileUpload).filter(FileUpload.user_id == current_user.id)
    
    if session_id:
        query = query.filter(FileUpload.session_id == session_id)
    
    files = query.order_by(FileUpload.created_at.desc()).offset(offset).limit(limit).all()
    
    return [FileUploadResponse.from_orm(file_upload) for file_upload in files]


@router.get("/{file_id}", response_model=FileUploadResponse)
async def get_file(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific file."""
    file_upload = get_file_upload(db, file_id, current_user.id)
    
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    return FileUploadResponse.from_orm(file_upload)


@router.post("/{file_id}/process", response_model=FileUploadResponse)
async def process_file(
    file_id: int,
    background_tasks: BackgroundTasks,
    force_reprocess: bool = False,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Process or reprocess a file."""
    file_upload = get_file_upload(db, file_id, current_user.id)
    
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    # Check if already processed and not forcing reprocess
    if file_upload.is_processed and not force_reprocess:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File already processed. Use force_reprocess=true to reprocess."
        )
    
    # Check if currently processing
    if file_upload.processing_status == "processing":
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="File is currently being processed"
        )
    
    # Reset processing status
    file_upload.processing_status = "pending"
    file_upload.processing_error = None
    file_upload.extracted_text = None
    file_upload.processed_at = None
    db.commit()
    
    # Start background processing
    from ..core.database import SessionLocal
    background_tasks.add_task(
        process_file_background,
        file_upload.id,
        SessionLocal
    )
    
    logger.info("File processing started", file_id=file_id, user_id=current_user.id)
    
    return FileUploadResponse.from_orm(file_upload)


@router.delete("/{file_id}")
async def delete_file(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a file."""
    file_upload = get_file_upload(db, file_id, current_user.id)
    
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    try:
        # Delete file from disk
        if os.path.exists(file_upload.file_path):
            os.remove(file_upload.file_path)
        
        # Delete database record
        db.delete(file_upload)
        db.commit()
        
        logger.info("File deleted", file_id=file_id, user_id=current_user.id)
        
        return {"message": "File deleted successfully"}
        
    except Exception as e:
        logger.error("File deletion failed", file_id=file_id, error=str(e))
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="File deletion failed"
        )


@router.get("/{file_id}/download")
async def download_file(
    file_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Download a file."""
    file_upload = get_file_upload(db, file_id, current_user.id)
    
    if not file_upload:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found"
        )
    
    if not os.path.exists(file_upload.file_path):
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="File not found on disk"
        )
    
    from fastapi.responses import FileResponse
    
    return FileResponse(
        path=file_upload.file_path,
        filename=file_upload.original_filename,
        media_type=file_upload.mime_type
    )


@router.get("/formats/supported")
async def get_supported_formats():
    """Get list of supported file formats."""
    return {
        "supported_formats": file_processor.get_supported_formats(),
        "max_file_size": settings.MAX_FILE_SIZE,
        "allowed_extensions": settings.ALLOWED_FILE_TYPES
    }


@router.get("/stats/processing")
async def get_processing_stats(current_user: User = Depends(get_current_active_user)):
    """Get file processing statistics."""
    return {
        "user_stats": "User-specific stats would be implemented here",
        "global_stats": file_processor.get_processing_stats()
    }
