"""
Chat API endpoints with WebSocket support.
"""
import asyncio
import json
from datetime import datetime
from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, WebSocket, WebSocketDisconnect, status
from sqlalchemy.orm import Session
from pydantic import BaseModel
import structlog

from ..core.database import get_db
from ..core.security import get_current_active_user, verify_token
from ..core.config import settings
from ..models.user import User
from ..models.chat import ChatSession, Message, MessageType, MessageStatus
from ..agents.dynamic_router import router as ai_router
from ..tools.tool_registry import tool_registry

logger = structlog.get_logger(__name__)

router = APIRouter()


# Pydantic models
class ChatSessionCreate(BaseModel):
    """Chat session creation model."""
    title: Optional[str] = "New Chat"
    description: Optional[str] = None
    model_name: Optional[str] = None
    system_prompt: Optional[str] = None
    temperature: Optional[str] = "0.7"
    max_tokens: Optional[int] = 2048


class ChatSessionResponse(BaseModel):
    """Chat session response model."""
    id: int
    title: str
    description: Optional[str]
    model_name: str
    system_prompt: Optional[str]
    temperature: str
    max_tokens: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    last_message_at: Optional[datetime]
    message_count: int = 0

    class Config:
        from_attributes = True


class MessageCreate(BaseModel):
    """Message creation model."""
    content: str
    message_type: MessageType = MessageType.USER
    file_attachments: Optional[List[Dict[str, Any]]] = None


class MessageResponse(BaseModel):
    """Message response model."""
    id: int
    content: str
    message_type: MessageType
    status: MessageStatus
    model_used: Optional[str]
    tokens_used: Optional[int]
    processing_time: Optional[str]
    tool_calls: Optional[List[Dict[str, Any]]]
    tool_results: Optional[List[Dict[str, Any]]]
    file_attachments: Optional[List[Dict[str, Any]]]
    error_message: Optional[str]
    created_at: datetime

    class Config:
        from_attributes = True


class ChatRequest(BaseModel):
    """Chat request model for WebSocket."""
    session_id: int
    message: str
    file_attachments: Optional[List[Dict[str, Any]]] = None
    user_preferences: Optional[Dict[str, Any]] = None


# WebSocket connection manager
class ConnectionManager:
    """Manage WebSocket connections."""
    
    def __init__(self):
        self.active_connections: Dict[int, List[WebSocket]] = {}
    
    async def connect(self, websocket: WebSocket, user_id: int):
        """Connect a WebSocket for a user."""
        await websocket.accept()
        
        if user_id not in self.active_connections:
            self.active_connections[user_id] = []
        
        self.active_connections[user_id].append(websocket)
        logger.info("WebSocket connected", user_id=user_id, total_connections=len(self.active_connections))
    
    def disconnect(self, websocket: WebSocket, user_id: int):
        """Disconnect a WebSocket."""
        if user_id in self.active_connections:
            if websocket in self.active_connections[user_id]:
                self.active_connections[user_id].remove(websocket)
            
            if not self.active_connections[user_id]:
                del self.active_connections[user_id]
        
        logger.info("WebSocket disconnected", user_id=user_id, total_connections=len(self.active_connections))
    
    async def send_personal_message(self, message: dict, user_id: int):
        """Send a message to all connections of a specific user."""
        if user_id in self.active_connections:
            disconnected = []
            
            for websocket in self.active_connections[user_id]:
                try:
                    await websocket.send_text(json.dumps(message))
                except Exception as e:
                    logger.warning("Failed to send message", user_id=user_id, error=str(e))
                    disconnected.append(websocket)
            
            # Remove disconnected websockets
            for ws in disconnected:
                self.disconnect(ws, user_id)


manager = ConnectionManager()


# Helper functions
def get_chat_session(db: Session, session_id: int, user_id: int) -> Optional[ChatSession]:
    """Get chat session by ID and user."""
    return db.query(ChatSession).filter(
        ChatSession.id == session_id,
        ChatSession.user_id == user_id
    ).first()


def create_chat_session(db: Session, session_create: ChatSessionCreate, user_id: int) -> ChatSession:
    """Create a new chat session."""
    session = ChatSession(
        user_id=user_id,
        title=session_create.title or "New Chat",
        description=session_create.description,
        model_name=session_create.model_name or settings.DEFAULT_MODEL,
        system_prompt=session_create.system_prompt,
        temperature=session_create.temperature or "0.7",
        max_tokens=session_create.max_tokens or 2048
    )
    
    db.add(session)
    db.commit()
    db.refresh(session)
    
    return session


def create_message(
    db: Session,
    session_id: int,
    user_id: int,
    content: str,
    message_type: MessageType = MessageType.USER,
    file_attachments: Optional[List[Dict[str, Any]]] = None
) -> Message:
    """Create a new message."""
    message = Message(
        session_id=session_id,
        user_id=user_id,
        content=content,
        message_type=message_type,
        file_attachments=file_attachments
    )
    
    db.add(message)
    db.commit()
    db.refresh(message)
    
    # Update session last_message_at
    session = db.query(ChatSession).filter(ChatSession.id == session_id).first()
    if session:
        session.last_message_at = datetime.utcnow()
        db.commit()
    
    return message


# REST API endpoints
@router.post("/sessions", response_model=ChatSessionResponse, status_code=status.HTTP_201_CREATED)
async def create_session(
    session_create: ChatSessionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Create a new chat session."""
    session = create_chat_session(db, session_create, current_user.id)
    
    logger.info("Chat session created", session_id=session.id, user_id=current_user.id)
    
    response = ChatSessionResponse.from_orm(session)
    response.message_count = 0
    
    return response


@router.get("/sessions", response_model=List[ChatSessionResponse])
async def get_sessions(
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    limit: int = 50,
    offset: int = 0
):
    """Get user's chat sessions."""
    sessions = db.query(ChatSession).filter(
        ChatSession.user_id == current_user.id,
        ChatSession.is_active == True
    ).order_by(ChatSession.updated_at.desc()).offset(offset).limit(limit).all()
    
    # Add message count to each session
    response_sessions = []
    for session in sessions:
        session_response = ChatSessionResponse.from_orm(session)
        session_response.message_count = len(session.messages)
        response_sessions.append(session_response)
    
    return response_sessions


@router.get("/sessions/{session_id}", response_model=ChatSessionResponse)
async def get_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Get a specific chat session."""
    session = get_chat_session(db, session_id, current_user.id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    response = ChatSessionResponse.from_orm(session)
    response.message_count = len(session.messages)
    
    return response


@router.put("/sessions/{session_id}", response_model=ChatSessionResponse)
async def update_session(
    session_id: int,
    session_update: ChatSessionCreate,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Update a chat session."""
    session = get_chat_session(db, session_id, current_user.id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    # Update fields
    if session_update.title:
        session.title = session_update.title
    if session_update.description is not None:
        session.description = session_update.description
    if session_update.model_name:
        session.model_name = session_update.model_name
    if session_update.system_prompt is not None:
        session.system_prompt = session_update.system_prompt
    if session_update.temperature:
        session.temperature = session_update.temperature
    if session_update.max_tokens:
        session.max_tokens = session_update.max_tokens
    
    db.commit()
    db.refresh(session)
    
    logger.info("Chat session updated", session_id=session.id, user_id=current_user.id)
    
    response = ChatSessionResponse.from_orm(session)
    response.message_count = len(session.messages)
    
    return response


@router.delete("/sessions/{session_id}")
async def delete_session(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db)
):
    """Delete a chat session."""
    session = get_chat_session(db, session_id, current_user.id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    # Soft delete
    session.is_active = False
    db.commit()
    
    logger.info("Chat session deleted", session_id=session.id, user_id=current_user.id)
    
    return {"message": "Chat session deleted successfully"}


@router.get("/sessions/{session_id}/messages", response_model=List[MessageResponse])
async def get_messages(
    session_id: int,
    current_user: User = Depends(get_current_active_user),
    db: Session = Depends(get_db),
    limit: int = 100,
    offset: int = 0
):
    """Get messages from a chat session."""
    session = get_chat_session(db, session_id, current_user.id)
    
    if not session:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Chat session not found"
        )
    
    messages = db.query(Message).filter(
        Message.session_id == session_id
    ).order_by(Message.created_at.asc()).offset(offset).limit(limit).all()
    
    return [MessageResponse.from_orm(message) for message in messages]


# WebSocket endpoint
@router.websocket("/ws/{token}")
async def websocket_endpoint(websocket: WebSocket, token: str, db: Session = Depends(get_db)):
    """WebSocket endpoint for real-time chat."""
    # Verify token and get user
    user_id = verify_token(token)
    if not user_id:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    user = db.query(User).filter(User.id == user_id).first()
    if not user or not user.is_active:
        await websocket.close(code=status.WS_1008_POLICY_VIOLATION)
        return
    
    await manager.connect(websocket, user.id)
    
    try:
        while True:
            # Receive message from client
            data = await websocket.receive_text()
            
            try:
                chat_request = ChatRequest.parse_raw(data)
                
                # Verify user owns the session
                session = get_chat_session(db, chat_request.session_id, user.id)
                if not session:
                    await manager.send_personal_message({
                        "type": "error",
                        "message": "Chat session not found"
                    }, user.id)
                    continue
                
                # Create user message
                user_message = create_message(
                    db,
                    chat_request.session_id,
                    user.id,
                    chat_request.message,
                    MessageType.USER,
                    chat_request.file_attachments
                )
                
                # Send user message confirmation
                await manager.send_personal_message({
                    "type": "message",
                    "data": MessageResponse.from_orm(user_message).dict()
                }, user.id)
                
                # Process AI response
                await process_ai_response(
                    db, session, user, chat_request, manager
                )
                
            except Exception as e:
                logger.error("WebSocket message processing failed", error=str(e), user_id=user.id)
                await manager.send_personal_message({
                    "type": "error",
                    "message": "Failed to process message"
                }, user.id)
    
    except WebSocketDisconnect:
        manager.disconnect(websocket, user.id)
    except Exception as e:
        logger.error("WebSocket error", error=str(e), user_id=user.id)
        manager.disconnect(websocket, user.id)


async def process_ai_response(
    db: Session,
    session: ChatSession,
    user: User,
    chat_request: ChatRequest,
    manager: ConnectionManager
):
    """Process AI response for chat message."""
    try:
        # Create assistant message with pending status
        assistant_message = create_message(
            db,
            session.id,
            user.id,
            "",
            MessageType.ASSISTANT,
            None
        )
        assistant_message.status = MessageStatus.PROCESSING
        db.commit()
        
        # Send processing status
        await manager.send_personal_message({
            "type": "status",
            "message_id": assistant_message.id,
            "status": "processing"
        }, user.id)
        
        # Get conversation history
        messages = db.query(Message).filter(
            Message.session_id == session.id,
            Message.status == MessageStatus.COMPLETED
        ).order_by(Message.created_at.asc()).all()
        
        # Build conversation for AI
        conversation = []
        
        if session.system_prompt:
            conversation.append({
                "role": "system",
                "content": session.system_prompt
            })
        
        for msg in messages:
            role = "user" if msg.message_type == MessageType.USER else "assistant"
            conversation.append({
                "role": role,
                "content": msg.content
            })
        
        # Add current message
        conversation.append({
            "role": "user",
            "content": chat_request.message
        })
        
        # Route request to best model
        routing_decision = await ai_router.route_request(
            conversation,
            chat_request.user_preferences
        )
        
        # Complete with AI
        start_time = asyncio.get_event_loop().time()
        
        ai_response = await ai_router.complete_with_fallback(
            conversation,
            routing_decision,
            temperature=float(session.temperature),
            max_tokens=session.max_tokens
        )
        
        end_time = asyncio.get_event_loop().time()
        processing_time = end_time - start_time
        
        # Update assistant message
        assistant_message.content = ai_response["response"].choices[0].message.content
        assistant_message.status = MessageStatus.COMPLETED
        assistant_message.model_used = ai_response["model_used"]
        assistant_message.processing_time = str(int(processing_time * 1000))  # milliseconds
        
        if hasattr(ai_response["response"], 'usage'):
            assistant_message.tokens_used = ai_response["response"].usage.total_tokens
        
        db.commit()
        
        # Send completed message
        await manager.send_personal_message({
            "type": "message",
            "data": MessageResponse.from_orm(assistant_message).dict()
        }, user.id)
        
        logger.info(
            "AI response completed",
            session_id=session.id,
            user_id=user.id,
            model_used=ai_response["model_used"],
            processing_time=processing_time
        )
        
    except Exception as e:
        logger.error("AI response processing failed", error=str(e), session_id=session.id)
        
        # Update message with error
        assistant_message.status = MessageStatus.ERROR
        assistant_message.error_message = str(e)
        db.commit()
        
        # Send error message
        await manager.send_personal_message({
            "type": "error",
            "message": "Failed to generate AI response",
            "details": str(e)
        }, user.id)
