/**
 * File upload component with drag and drop support
 */
import React, { useCallback, useState } from 'react';
import { useDropzone } from 'react-dropzone';
import { Upload, X, File, AlertCircle, CheckCircle } from 'lucide-react';
import { useChatStore } from '../store/chatStore';
import { apiClient } from '../lib/api';

interface FileUploadProps {
  onFilesSelected: (files: File[]) => void;
  onClose: () => void;
  sessionId?: number;
}

const FileUpload: React.FC<FileUploadProps> = ({ onFilesSelected, onClose, sessionId }) => {
  const { currentSession, addFile } = useChatStore();
  const [uploading, setUploading] = useState(false);
  const [uploadProgress, setUploadProgress] = useState<{ [key: string]: number }>({});
  const [uploadErrors, setUploadErrors] = useState<{ [key: string]: string }>({});
  const [uploadedFiles, setUploadedFiles] = useState<File[]>([]);

  const maxFileSize = 50 * 1024 * 1024; // 50MB
  const acceptedFileTypes = {
    'application/pdf': ['.pdf'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx'],
    'text/plain': ['.txt'],
    'text/markdown': ['.md'],
    'text/csv': ['.csv'],
    'application/json': ['.json'],
    'application/xml': ['.xml'],
    'text/html': ['.html', '.htm']
  };

  const onDrop = useCallback(async (acceptedFiles: File[], rejectedFiles: any[]) => {
    // Handle rejected files
    const errors: { [key: string]: string } = {};
    rejectedFiles.forEach(({ file, errors: fileErrors }) => {
      const errorMessages = fileErrors.map((error: any) => {
        switch (error.code) {
          case 'file-too-large':
            return `File is too large. Maximum size is ${maxFileSize / (1024 * 1024)}MB.`;
          case 'file-invalid-type':
            return 'File type not supported.';
          default:
            return error.message;
        }
      });
      errors[file.name] = errorMessages.join(', ');
    });
    setUploadErrors(errors);

    if (acceptedFiles.length === 0) return;

    setUploading(true);
    const progress: { [key: string]: number } = {};
    
    // Initialize progress for all files
    acceptedFiles.forEach(file => {
      progress[file.name] = 0;
    });
    setUploadProgress(progress);

    try {
      const uploadPromises = acceptedFiles.map(async (file) => {
        try {
          // Simulate progress updates
          const progressInterval = setInterval(() => {
            setUploadProgress(prev => ({
              ...prev,
              [file.name]: Math.min(prev[file.name] + 10, 90)
            }));
          }, 200);

          const uploadedFile = await apiClient.uploadFile(
            file,
            sessionId || currentSession?.id
          );

          clearInterval(progressInterval);
          
          // Complete progress
          setUploadProgress(prev => ({
            ...prev,
            [file.name]: 100
          }));

          // Add to store
          addFile(uploadedFile);
          
          return file;
        } catch (error) {
          console.error(`Failed to upload ${file.name}:`, error);
          setUploadErrors(prev => ({
            ...prev,
            [file.name]: error instanceof Error ? error.message : 'Upload failed'
          }));
          return null;
        }
      });

      const results = await Promise.all(uploadPromises);
      const successfulFiles = results.filter((file): file is File => file !== null);
      
      setUploadedFiles(successfulFiles);
      onFilesSelected(successfulFiles);
      
    } catch (error) {
      console.error('Upload error:', error);
    } finally {
      setUploading(false);
    }
  }, [sessionId, currentSession?.id, addFile, onFilesSelected]);

  const { getRootProps, getInputProps, isDragActive } = useDropzone({
    onDrop,
    accept: acceptedFileTypes,
    maxSize: maxFileSize,
    multiple: true
  });

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const getFileIcon = (file: File) => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    switch (extension) {
      case 'pdf':
        return <div className="w-8 h-8 bg-red-100 text-red-600 rounded flex items-center justify-center text-xs font-bold">PDF</div>;
      case 'docx':
        return <div className="w-8 h-8 bg-blue-100 text-blue-600 rounded flex items-center justify-center text-xs font-bold">DOC</div>;
      case 'txt':
      case 'md':
        return <div className="w-8 h-8 bg-gray-100 text-gray-600 rounded flex items-center justify-center text-xs font-bold">TXT</div>;
      case 'csv':
        return <div className="w-8 h-8 bg-green-100 text-green-600 rounded flex items-center justify-center text-xs font-bold">CSV</div>;
      case 'json':
        return <div className="w-8 h-8 bg-yellow-100 text-yellow-600 rounded flex items-center justify-center text-xs font-bold">JSON</div>;
      default:
        return <File className="w-8 h-8 text-gray-400" />;
    }
  };

  return (
    <div className="bg-white border border-gray-200 rounded-lg shadow-lg p-4 max-w-md">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-lg font-semibold">Upload Files</h3>
        <button
          onClick={onClose}
          className="p-1 hover:bg-gray-100 rounded"
        >
          <X className="w-5 h-5" />
        </button>
      </div>

      {/* Dropzone */}
      <div
        {...getRootProps()}
        className={`border-2 border-dashed rounded-lg p-6 text-center cursor-pointer transition-colors ${
          isDragActive
            ? 'border-blue-400 bg-blue-50'
            : 'border-gray-300 hover:border-gray-400'
        }`}
      >
        <input {...getInputProps()} />
        <Upload className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        {isDragActive ? (
          <p className="text-blue-600">Drop the files here...</p>
        ) : (
          <div>
            <p className="text-gray-600 mb-1">
              Drag & drop files here, or click to select
            </p>
            <p className="text-sm text-gray-500">
              PDF, DOCX, TXT, MD, CSV, JSON, XML, HTML (max {maxFileSize / (1024 * 1024)}MB)
            </p>
          </div>
        )}
      </div>

      {/* Upload Progress */}
      {uploading && Object.keys(uploadProgress).length > 0 && (
        <div className="mt-4 space-y-3">
          <h4 className="font-medium text-gray-900">Uploading...</h4>
          {Object.entries(uploadProgress).map(([fileName, progress]) => (
            <div key={fileName} className="space-y-1">
              <div className="flex items-center justify-between text-sm">
                <span className="truncate">{fileName}</span>
                <span>{progress}%</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div
                  className="bg-blue-600 h-2 rounded-full transition-all duration-300"
                  style={{ width: `${progress}%` }}
                />
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Upload Errors */}
      {Object.keys(uploadErrors).length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="font-medium text-red-900 flex items-center">
            <AlertCircle className="w-4 h-4 mr-1" />
            Upload Errors
          </h4>
          {Object.entries(uploadErrors).map(([fileName, error]) => (
            <div key={fileName} className="text-sm text-red-600 bg-red-50 p-2 rounded">
              <div className="font-medium">{fileName}</div>
              <div>{error}</div>
            </div>
          ))}
        </div>
      )}

      {/* Successfully Uploaded Files */}
      {uploadedFiles.length > 0 && (
        <div className="mt-4 space-y-2">
          <h4 className="font-medium text-green-900 flex items-center">
            <CheckCircle className="w-4 h-4 mr-1" />
            Successfully Uploaded
          </h4>
          {uploadedFiles.map((file, index) => (
            <div key={index} className="flex items-center space-x-3 p-2 bg-green-50 rounded">
              {getFileIcon(file)}
              <div className="flex-1 min-w-0">
                <div className="text-sm font-medium text-gray-900 truncate">
                  {file.name}
                </div>
                <div className="text-xs text-gray-500">
                  {formatFileSize(file.size)}
                </div>
              </div>
            </div>
          ))}
        </div>
      )}

      {/* Actions */}
      <div className="mt-4 flex justify-end space-x-2">
        <button
          onClick={onClose}
          className="px-4 py-2 text-gray-600 hover:text-gray-800"
          disabled={uploading}
        >
          {uploadedFiles.length > 0 ? 'Done' : 'Cancel'}
        </button>
      </div>
    </div>
  );
};

export default FileUpload;
