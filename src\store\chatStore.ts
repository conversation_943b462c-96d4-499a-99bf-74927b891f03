/**
 * Chat store using Zustand for state management
 */
import { create } from 'zustand';
import { devtools } from 'zustand/middleware';

export interface Message {
  id: number;
  content: string;
  message_type: 'user' | 'assistant' | 'system' | 'tool';
  status: 'pending' | 'processing' | 'completed' | 'error';
  model_used?: string;
  tokens_used?: number;
  processing_time?: string;
  tool_calls?: any[];
  tool_results?: any[];
  file_attachments?: any[];
  error_message?: string;
  created_at: string;
}

export interface ChatSession {
  id: number;
  title: string;
  description?: string;
  model_name: string;
  system_prompt?: string;
  temperature: string;
  max_tokens: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  last_message_at?: string;
  message_count: number;
}

export interface FileUpload {
  id: number;
  filename: string;
  original_filename: string;
  file_size: number;
  file_type: string;
  mime_type: string;
  is_processed: boolean;
  processing_status: string;
  extracted_text?: string;
  processing_error?: string;
  created_at: string;
  processed_at?: string;
}

interface ChatState {
  // Sessions
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  
  // Messages
  messages: Message[];
  isLoading: boolean;
  
  // Files
  files: FileUpload[];
  
  // WebSocket
  isConnected: boolean;
  
  // UI State
  sidebarOpen: boolean;
  
  // Actions
  setSessions: (sessions: ChatSession[]) => void;
  setCurrentSession: (session: ChatSession | null) => void;
  addSession: (session: ChatSession) => void;
  updateSession: (sessionId: number, updates: Partial<ChatSession>) => void;
  deleteSession: (sessionId: number) => void;
  
  setMessages: (messages: Message[]) => void;
  addMessage: (message: Message) => void;
  updateMessage: (messageId: number, updates: Partial<Message>) => void;
  
  setFiles: (files: FileUpload[]) => void;
  addFile: (file: FileUpload) => void;
  updateFile: (fileId: number, updates: Partial<FileUpload>) => void;
  removeFile: (fileId: number) => void;
  
  setIsLoading: (loading: boolean) => void;
  setIsConnected: (connected: boolean) => void;
  setSidebarOpen: (open: boolean) => void;
  
  // Clear all data (for logout)
  clearAll: () => void;
}

export const useChatStore = create<ChatState>()(
  devtools(
    (set, get) => ({
      // Initial state
      sessions: [],
      currentSession: null,
      messages: [],
      isLoading: false,
      files: [],
      isConnected: false,
      sidebarOpen: true,
      
      // Session actions
      setSessions: (sessions) => set({ sessions }),
      
      setCurrentSession: (session) => set({ currentSession: session }),
      
      addSession: (session) => set((state) => ({
        sessions: [session, ...state.sessions]
      })),
      
      updateSession: (sessionId, updates) => set((state) => ({
        sessions: state.sessions.map(session =>
          session.id === sessionId ? { ...session, ...updates } : session
        ),
        currentSession: state.currentSession?.id === sessionId
          ? { ...state.currentSession, ...updates }
          : state.currentSession
      })),
      
      deleteSession: (sessionId) => set((state) => ({
        sessions: state.sessions.filter(session => session.id !== sessionId),
        currentSession: state.currentSession?.id === sessionId ? null : state.currentSession,
        messages: state.currentSession?.id === sessionId ? [] : state.messages
      })),
      
      // Message actions
      setMessages: (messages) => set({ messages }),
      
      addMessage: (message) => set((state) => ({
        messages: [...state.messages, message]
      })),
      
      updateMessage: (messageId, updates) => set((state) => ({
        messages: state.messages.map(message =>
          message.id === messageId ? { ...message, ...updates } : message
        )
      })),
      
      // File actions
      setFiles: (files) => set({ files }),
      
      addFile: (file) => set((state) => ({
        files: [file, ...state.files]
      })),
      
      updateFile: (fileId, updates) => set((state) => ({
        files: state.files.map(file =>
          file.id === fileId ? { ...file, ...updates } : file
        )
      })),
      
      removeFile: (fileId) => set((state) => ({
        files: state.files.filter(file => file.id !== fileId)
      })),
      
      // UI actions
      setIsLoading: (loading) => set({ isLoading: loading }),
      setIsConnected: (connected) => set({ isConnected: connected }),
      setSidebarOpen: (open) => set({ sidebarOpen: open }),
      
      // Clear all data
      clearAll: () => set({
        sessions: [],
        currentSession: null,
        messages: [],
        files: [],
        isLoading: false,
        isConnected: false,
        sidebarOpen: true
      })
    }),
    {
      name: 'chat-store'
    }
  )
);
