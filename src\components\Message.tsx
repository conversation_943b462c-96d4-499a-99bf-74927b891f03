/**
 * Message component for displaying chat messages
 */
import React, { useState } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>, CheckCircle, XCircle, Copy, MoreHorizontal } from 'lucide-react';
import { Message } from '../store/chatStore';

interface MessageProps {
  message: Message;
}

const MessageComponent: React.FC<MessageProps> = ({ message }) => {
  const [showDetails, setShowDetails] = useState(false);
  const [copied, setCopied] = useState(false);

  const isUser = message.message_type === 'user';
  const isAssistant = message.message_type === 'assistant';
  const isSystem = message.message_type === 'system';
  const isTool = message.message_type === 'tool';

  const getStatusIcon = () => {
    switch (message.status) {
      case 'pending':
        return <Clock className="w-4 h-4 text-gray-400" />;
      case 'processing':
        return <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin" />;
      case 'completed':
        return <CheckCircle className="w-4 h-4 text-green-500" />;
      case 'error':
        return <XCircle className="w-4 h-4 text-red-500" />;
      default:
        return null;
    }
  };

  const getMessageIcon = () => {
    if (isUser) {
      return <User className="w-6 h-6 text-blue-600" />;
    } else if (isAssistant) {
      return <Bot className="w-6 h-6 text-purple-600" />;
    } else if (isSystem) {
      return <div className="w-6 h-6 bg-gray-400 rounded-full flex items-center justify-center text-white text-xs font-bold">S</div>;
    } else if (isTool) {
      return <div className="w-6 h-6 bg-orange-400 rounded-full flex items-center justify-center text-white text-xs font-bold">T</div>;
    }
    return null;
  };

  const copyToClipboard = async () => {
    try {
      await navigator.clipboard.writeText(message.content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (error) {
      console.error('Failed to copy message:', error);
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    const now = new Date();
    const diffInHours = (now.getTime() - date.getTime()) / (1000 * 60 * 60);

    if (diffInHours < 24) {
      return date.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });
    } else {
      return date.toLocaleDateString([], { month: 'short', day: 'numeric' });
    }
  };

  const renderContent = () => {
    // Handle different content types
    if (message.error_message) {
      return (
        <div className="text-red-600 bg-red-50 p-3 rounded-lg border border-red-200">
          <div className="font-medium">Error</div>
          <div className="text-sm mt-1">{message.error_message}</div>
        </div>
      );
    }

    // Basic text content with simple formatting
    const content = message.content || '';
    
    // Split by code blocks
    const parts = content.split(/(```[\s\S]*?```|`[^`]+`)/);
    
    return (
      <div className="prose prose-sm max-w-none">
        {parts.map((part, index) => {
          if (part.startsWith('```') && part.endsWith('```')) {
            // Code block
            const code = part.slice(3, -3);
            const lines = code.split('\n');
            const language = lines[0].trim();
            const codeContent = lines.slice(1).join('\n');
            
            return (
              <div key={index} className="my-3">
                <div className="bg-gray-900 text-gray-100 rounded-lg overflow-hidden">
                  {language && (
                    <div className="px-4 py-2 bg-gray-800 text-sm text-gray-300 border-b border-gray-700">
                      {language}
                    </div>
                  )}
                  <pre className="p-4 overflow-x-auto">
                    <code>{codeContent}</code>
                  </pre>
                </div>
              </div>
            );
          } else if (part.startsWith('`') && part.endsWith('`')) {
            // Inline code
            return (
              <code key={index} className="bg-gray-100 text-gray-800 px-1 py-0.5 rounded text-sm">
                {part.slice(1, -1)}
              </code>
            );
          } else {
            // Regular text with line breaks
            return (
              <span key={index}>
                {part.split('\n').map((line, lineIndex) => (
                  <React.Fragment key={lineIndex}>
                    {line}
                    {lineIndex < part.split('\n').length - 1 && <br />}
                  </React.Fragment>
                ))}
              </span>
            );
          }
        })}
      </div>
    );
  };

  return (
    <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-4`}>
      <div className={`flex max-w-[80%] ${isUser ? 'flex-row-reverse' : 'flex-row'} items-start space-x-3`}>
        {/* Avatar */}
        <div className="flex-shrink-0">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center ${
            isUser ? 'bg-blue-100' : isAssistant ? 'bg-purple-100' : 'bg-gray-100'
          }`}>
            {getMessageIcon()}
          </div>
        </div>

        {/* Message Content */}
        <div className={`flex-1 ${isUser ? 'mr-3' : 'ml-3'}`}>
          <div className={`rounded-lg px-4 py-3 ${
            isUser 
              ? 'bg-blue-600 text-white' 
              : isSystem
              ? 'bg-gray-100 text-gray-800 border border-gray-200'
              : isTool
              ? 'bg-orange-50 text-orange-900 border border-orange-200'
              : 'bg-gray-50 text-gray-900 border border-gray-200'
          }`}>
            {renderContent()}

            {/* File Attachments */}
            {message.file_attachments && message.file_attachments.length > 0 && (
              <div className="mt-3 space-y-2">
                {message.file_attachments.map((file, index) => (
                  <div key={index} className="flex items-center space-x-2 text-sm">
                    <div className="w-4 h-4 bg-gray-400 rounded"></div>
                    <span>{file.name || 'Attached file'}</span>
                  </div>
                ))}
              </div>
            )}

            {/* Tool Calls */}
            {message.tool_calls && message.tool_calls.length > 0 && (
              <div className="mt-3 text-sm opacity-75">
                <div className="font-medium">Tool calls:</div>
                {message.tool_calls.map((tool, index) => (
                  <div key={index} className="ml-2">
                    • {tool.name || 'Unknown tool'}
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Message Footer */}
          <div className={`flex items-center justify-between mt-2 text-xs text-gray-500 ${
            isUser ? 'flex-row-reverse' : 'flex-row'
          }`}>
            <div className="flex items-center space-x-2">
              <span>{formatTimestamp(message.created_at)}</span>
              {getStatusIcon()}
              {message.model_used && (
                <>
                  <span>•</span>
                  <span>{message.model_used}</span>
                </>
              )}
              {message.processing_time && (
                <>
                  <span>•</span>
                  <span>{message.processing_time}ms</span>
                </>
              )}
            </div>

            <div className="flex items-center space-x-1">
              <button
                onClick={copyToClipboard}
                className="p-1 hover:bg-gray-200 rounded transition-colors"
                title="Copy message"
              >
                <Copy className="w-3 h-3" />
              </button>
              
              {(message.tokens_used || message.tool_results) && (
                <button
                  onClick={() => setShowDetails(!showDetails)}
                  className="p-1 hover:bg-gray-200 rounded transition-colors"
                  title="Show details"
                >
                  <MoreHorizontal className="w-3 h-3" />
                </button>
              )}
            </div>
          </div>

          {/* Copy Feedback */}
          {copied && (
            <div className="text-xs text-green-600 mt-1">
              Copied to clipboard!
            </div>
          )}

          {/* Detailed Information */}
          {showDetails && (
            <div className="mt-3 p-3 bg-gray-50 rounded-lg text-sm">
              <div className="space-y-2">
                {message.tokens_used && (
                  <div>
                    <span className="font-medium">Tokens used:</span> {message.tokens_used}
                  </div>
                )}
                
                {message.tool_results && message.tool_results.length > 0 && (
                  <div>
                    <span className="font-medium">Tool results:</span>
                    <pre className="mt-1 text-xs bg-white p-2 rounded border overflow-x-auto">
                      {JSON.stringify(message.tool_results, null, 2)}
                    </pre>
                  </div>
                )}
                
                <div>
                  <span className="font-medium">Message ID:</span> {message.id}
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default MessageComponent;
