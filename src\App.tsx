import React, { useEffect, useState } from 'react';
import { BrowserRouter as Router, Routes, Route, useLocation } from 'react-router-dom';
import NotFound from './components/NotFound';
import Chat from './components/Chat';
import Auth from './components/Auth';
import { initGA, trackPageView } from './utils/analytics';
import { apiClient } from './lib/api';

// Initialize analytics
initGA();

// Component to track page views
function PageViewTracker() {
  const location = useLocation();

  useEffect(() => {
    trackPageView(location.pathname);
  }, [location]);

  return null;
}

function Home() {
  return (
    <div className="min-h-screen bg-black relative overflow-hidden">
      {/* Subtle grid overlay */}
      <div className="absolute inset-0 bg-[linear-gradient(rgba(255,255,255,0.02)_1px,transparent_1px),linear-gradient(90deg,rgba(255,255,255,0.02)_1px,transparent_1px)] bg-[size:50px_50px]" />

      {/* Main Content */}
      <div className="relative z-10 flex flex-col items-center justify-center min-h-screen px-4 text-center">
        {/* Logo/Brand */}
        <div className="mb-12 relative">
          <h1 className="text-6xl md:text-8xl lg:text-9xl font-bold text-white leading-tight tracking-tight select-none outline-text">
            */now<span className="text-white">.</span>
          </h1>
        </div>

        {/* Description */}
        <div className="mb-16 max-w-2xl">
          <div className="bg-white/5 backdrop-blur-sm border border-white/10 rounded-lg px-8 py-6 shadow-lg">
            <p className="text-lg md:text-xl text-white/90 font-medium tracking-wide mb-4">
              AI-Powered Chat Platform
            </p>
            <p className="text-sm md:text-base text-white/70 mb-6">
              Experience intelligent conversations with dynamic model routing,
              file processing, and advanced tool integration.
            </p>
            <a
              href="/chat"
              className="inline-flex items-center px-6 py-3 bg-blue-600 hover:bg-blue-700 text-white font-medium rounded-lg transition-colors"
            >
              Get Started
            </a>
          </div>
        </div>

        {/* Minimal decorative elements */}
        <div className="w-16 h-px bg-white/20 mb-8" />
      </div>
    </div>
  );
}

function App() {
  const [isAuthenticated, setIsAuthenticated] = useState<boolean | null>(null);

  useEffect(() => {
    // Check authentication status on app load
    setIsAuthenticated(apiClient.isAuthenticated());
  }, []);

  const handleAuthSuccess = () => {
    setIsAuthenticated(true);
  };

  const handleLogout = async () => {
    await apiClient.logout();
    setIsAuthenticated(false);
  };

  // Show loading while checking auth status
  if (isAuthenticated === null) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-white mx-auto mb-4"></div>
          <p className="text-white">Loading...</p>
        </div>
      </div>
    );
  }

  return (
    <Router>
      <PageViewTracker />
      <Routes>
        <Route path="/" element={<Home />} />
        <Route
          path="/chat"
          element={
            isAuthenticated ? (
              <Chat />
            ) : (
              <Auth onAuthSuccess={handleAuthSuccess} />
            )
          }
        />
        <Route
          path="/login"
          element={<Auth onAuthSuccess={handleAuthSuccess} />}
        />
        <Route path="*" element={<NotFound />} />
      </Routes>
    </Router>
  );
}

export default App;















