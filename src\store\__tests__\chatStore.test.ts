import { describe, it, expect, beforeEach } from 'vitest';
import { useChatStore } from '../chatStore';
import type { ChatSession, Message, FileUpload } from '../chatStore';

// Helper to get a fresh store instance
const getStore = () => useChatStore.getState();

describe('Chat Store', () => {
  beforeEach(() => {
    // Reset store before each test
    useChatStore.getState().clearAll();
  });

  describe('Sessions', () => {
    it('should initialize with empty sessions', () => {
      const store = getStore();
      expect(store.sessions).toEqual([]);
      expect(store.currentSession).toBeNull();
    });

    it('should set sessions', () => {
      const sessions: ChatSession[] = [
        {
          id: 1,
          title: 'Test Chat',
          model_name: 'gpt-3.5-turbo',
          temperature: '0.7',
          max_tokens: 2048,
          is_active: true,
          created_at: '2023-01-01T00:00:00Z',
          updated_at: '2023-01-01T00:00:00Z',
          message_count: 0,
        },
      ];

      useChatStore.getState().setSessions(sessions);
      expect(getStore().sessions).toEqual(sessions);
    });

    it('should add a new session', () => {
      const newSession: ChatSession = {
        id: 1,
        title: 'New Chat',
        model_name: 'gpt-4',
        temperature: '0.5',
        max_tokens: 4096,
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        message_count: 0,
      };

      useChatStore.getState().addSession(newSession);
      
      const store = getStore();
      expect(store.sessions).toHaveLength(1);
      expect(store.sessions[0]).toEqual(newSession);
    });

    it('should update a session', () => {
      const session: ChatSession = {
        id: 1,
        title: 'Original Title',
        model_name: 'gpt-3.5-turbo',
        temperature: '0.7',
        max_tokens: 2048,
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        message_count: 0,
      };

      useChatStore.getState().setSessions([session]);
      useChatStore.getState().setCurrentSession(session);

      const updates = { title: 'Updated Title', temperature: '0.9' };
      useChatStore.getState().updateSession(1, updates);

      const store = getStore();
      expect(store.sessions[0].title).toBe('Updated Title');
      expect(store.sessions[0].temperature).toBe('0.9');
      expect(store.currentSession?.title).toBe('Updated Title');
      expect(store.currentSession?.temperature).toBe('0.9');
    });

    it('should delete a session', () => {
      const session: ChatSession = {
        id: 1,
        title: 'Test Chat',
        model_name: 'gpt-3.5-turbo',
        temperature: '0.7',
        max_tokens: 2048,
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        message_count: 0,
      };

      useChatStore.getState().setSessions([session]);
      useChatStore.getState().setCurrentSession(session);

      useChatStore.getState().deleteSession(1);

      const store = getStore();
      expect(store.sessions).toHaveLength(0);
      expect(store.currentSession).toBeNull();
      expect(store.messages).toEqual([]);
    });
  });

  describe('Messages', () => {
    it('should initialize with empty messages', () => {
      const store = getStore();
      expect(store.messages).toEqual([]);
    });

    it('should set messages', () => {
      const messages: Message[] = [
        {
          id: 1,
          content: 'Hello',
          message_type: 'user',
          status: 'completed',
          created_at: '2023-01-01T00:00:00Z',
        },
      ];

      useChatStore.getState().setMessages(messages);
      expect(getStore().messages).toEqual(messages);
    });

    it('should add a message', () => {
      const message: Message = {
        id: 1,
        content: 'Hello',
        message_type: 'user',
        status: 'completed',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().addMessage(message);
      
      const store = getStore();
      expect(store.messages).toHaveLength(1);
      expect(store.messages[0]).toEqual(message);
    });

    it('should update a message', () => {
      const message: Message = {
        id: 1,
        content: 'Hello',
        message_type: 'user',
        status: 'pending',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().setMessages([message]);

      const updates = { status: 'completed' as const, content: 'Updated content' };
      useChatStore.getState().updateMessage(1, updates);

      const store = getStore();
      expect(store.messages[0].status).toBe('completed');
      expect(store.messages[0].content).toBe('Updated content');
    });
  });

  describe('Files', () => {
    it('should initialize with empty files', () => {
      const store = getStore();
      expect(store.files).toEqual([]);
    });

    it('should add a file', () => {
      const file: FileUpload = {
        id: 1,
        filename: 'test.pdf',
        original_filename: 'test.pdf',
        file_size: 1024,
        file_type: '.pdf',
        mime_type: 'application/pdf',
        is_processed: false,
        processing_status: 'pending',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().addFile(file);
      
      const store = getStore();
      expect(store.files).toHaveLength(1);
      expect(store.files[0]).toEqual(file);
    });

    it('should update a file', () => {
      const file: FileUpload = {
        id: 1,
        filename: 'test.pdf',
        original_filename: 'test.pdf',
        file_size: 1024,
        file_type: '.pdf',
        mime_type: 'application/pdf',
        is_processed: false,
        processing_status: 'pending',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().setFiles([file]);

      const updates = { is_processed: true, processing_status: 'completed' };
      useChatStore.getState().updateFile(1, updates);

      const store = getStore();
      expect(store.files[0].is_processed).toBe(true);
      expect(store.files[0].processing_status).toBe('completed');
    });

    it('should remove a file', () => {
      const file: FileUpload = {
        id: 1,
        filename: 'test.pdf',
        original_filename: 'test.pdf',
        file_size: 1024,
        file_type: '.pdf',
        mime_type: 'application/pdf',
        is_processed: false,
        processing_status: 'pending',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().setFiles([file]);
      useChatStore.getState().removeFile(1);

      const store = getStore();
      expect(store.files).toHaveLength(0);
    });
  });

  describe('UI State', () => {
    it('should manage loading state', () => {
      expect(getStore().isLoading).toBe(false);

      useChatStore.getState().setIsLoading(true);
      expect(getStore().isLoading).toBe(true);

      useChatStore.getState().setIsLoading(false);
      expect(getStore().isLoading).toBe(false);
    });

    it('should manage connection state', () => {
      expect(getStore().isConnected).toBe(false);

      useChatStore.getState().setIsConnected(true);
      expect(getStore().isConnected).toBe(true);

      useChatStore.getState().setIsConnected(false);
      expect(getStore().isConnected).toBe(false);
    });

    it('should manage sidebar state', () => {
      expect(getStore().sidebarOpen).toBe(true);

      useChatStore.getState().setSidebarOpen(false);
      expect(getStore().sidebarOpen).toBe(false);

      useChatStore.getState().setSidebarOpen(true);
      expect(getStore().sidebarOpen).toBe(true);
    });
  });

  describe('Clear All', () => {
    it('should clear all data', () => {
      // Set up some data
      const session: ChatSession = {
        id: 1,
        title: 'Test',
        model_name: 'gpt-3.5-turbo',
        temperature: '0.7',
        max_tokens: 2048,
        is_active: true,
        created_at: '2023-01-01T00:00:00Z',
        updated_at: '2023-01-01T00:00:00Z',
        message_count: 0,
      };

      const message: Message = {
        id: 1,
        content: 'Hello',
        message_type: 'user',
        status: 'completed',
        created_at: '2023-01-01T00:00:00Z',
      };

      const file: FileUpload = {
        id: 1,
        filename: 'test.pdf',
        original_filename: 'test.pdf',
        file_size: 1024,
        file_type: '.pdf',
        mime_type: 'application/pdf',
        is_processed: false,
        processing_status: 'pending',
        created_at: '2023-01-01T00:00:00Z',
      };

      useChatStore.getState().setSessions([session]);
      useChatStore.getState().setCurrentSession(session);
      useChatStore.getState().setMessages([message]);
      useChatStore.getState().setFiles([file]);
      useChatStore.getState().setIsLoading(true);
      useChatStore.getState().setIsConnected(true);
      useChatStore.getState().setSidebarOpen(false);

      // Clear all
      useChatStore.getState().clearAll();

      const store = getStore();
      expect(store.sessions).toEqual([]);
      expect(store.currentSession).toBeNull();
      expect(store.messages).toEqual([]);
      expect(store.files).toEqual([]);
      expect(store.isLoading).toBe(false);
      expect(store.isConnected).toBe(false);
      expect(store.sidebarOpen).toBe(true);
    });
  });
});
