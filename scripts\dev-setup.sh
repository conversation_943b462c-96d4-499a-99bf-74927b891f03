#!/bin/bash

# NextNow Development Setup Script
set -e

echo "🚀 Setting up NextNow development environment..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    print_error "Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    print_error "Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Check if Node.js is installed
if ! command -v node &> /dev/null; then
    print_warning "Node.js is not installed. You'll need it for local frontend development."
fi

# Check if Python is installed
if ! command -v python3 &> /dev/null; then
    print_warning "Python 3 is not installed. You'll need it for local backend development."
fi

print_status "Creating environment files..."

# Create backend .env file if it doesn't exist
if [ ! -f "backend/.env" ]; then
    cp backend/.env.example backend/.env
    print_success "Created backend/.env from example"
else
    print_warning "backend/.env already exists, skipping..."
fi

# Create frontend .env file if it doesn't exist
if [ ! -f ".env" ]; then
    cp .env.example .env
    print_success "Created .env from example"
else
    print_warning ".env already exists, skipping..."
fi

print_status "Creating necessary directories..."

# Create directories
mkdir -p backend/uploads
mkdir -p backend/logs
mkdir -p monitoring/grafana/dashboards
mkdir -p monitoring/grafana/datasources
mkdir -p nginx/ssl

print_success "Directories created"

print_status "Setting up Docker volumes..."

# Create Docker volumes
docker volume create nextnow_postgres_data
docker volume create nextnow_redis_data
docker volume create nextnow_prometheus_data
docker volume create nextnow_grafana_data

print_success "Docker volumes created"

print_status "Building Docker images..."

# Build images
docker-compose build

print_success "Docker images built"

print_status "Starting services..."

# Start services
docker-compose up -d postgres redis

print_status "Waiting for database to be ready..."
sleep 10

# Run database migrations
print_status "Running database migrations..."
docker-compose run --rm backend alembic upgrade head

print_success "Database migrations completed"

print_status "Starting all services..."

# Start all services
docker-compose up -d

print_success "All services started"

print_status "Installing frontend dependencies..."

# Install frontend dependencies if Node.js is available
if command -v npm &> /dev/null; then
    npm install
    print_success "Frontend dependencies installed"
else
    print_warning "Skipping frontend dependency installation (Node.js not found)"
fi

print_status "Installing backend dependencies..."

# Install backend dependencies if Python is available
if command -v python3 &> /dev/null && command -v pip &> /dev/null; then
    cd backend
    pip install -r requirements.txt
    cd ..
    print_success "Backend dependencies installed"
else
    print_warning "Skipping backend dependency installation (Python/pip not found)"
fi

echo ""
print_success "🎉 NextNow development environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "  1. Update your API keys in backend/.env"
echo "  2. Access the application:"
echo "     • Frontend: http://localhost:3000"
echo "     • Backend API: http://localhost:8000"
echo "     • API Docs: http://localhost:8000/docs"
echo "     • Grafana: http://localhost:3001 (admin/admin)"
echo "     • Prometheus: http://localhost:9090"
echo ""
echo "🛠️  Development commands:"
echo "  • Start services: docker-compose up -d"
echo "  • Stop services: docker-compose down"
echo "  • View logs: docker-compose logs -f [service]"
echo "  • Frontend dev: npm run dev"
echo "  • Backend dev: cd backend && uvicorn app.main:app --reload"
echo ""
echo "🧪 Testing:"
echo "  • Frontend tests: npm test"
echo "  • Backend tests: cd backend && pytest"
echo ""
echo "📚 Documentation: Check README.md for more details"
